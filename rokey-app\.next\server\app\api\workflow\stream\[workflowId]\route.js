/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/workflow/stream/[workflowId]/route";
exports.ids = ["app/api/workflow/stream/[workflowId]/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!************************************************************!*\
  !*** ./node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fworkflow%2Fstream%2F%5BworkflowId%5D%2Froute&page=%2Fapi%2Fworkflow%2Fstream%2F%5BworkflowId%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fworkflow%2Fstream%2F%5BworkflowId%5D%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fworkflow%2Fstream%2F%5BworkflowId%5D%2Froute&page=%2Fapi%2Fworkflow%2Fstream%2F%5BworkflowId%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fworkflow%2Fstream%2F%5BworkflowId%5D%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_RoKey_App_rokey_app_src_app_api_workflow_stream_workflowId_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/workflow/stream/[workflowId]/route.ts */ \"(rsc)/./src/app/api/workflow/stream/[workflowId]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/workflow/stream/[workflowId]/route\",\n        pathname: \"/api/workflow/stream/[workflowId]\",\n        filename: \"route\",\n        bundlePath: \"app/api/workflow/stream/[workflowId]/route\"\n    },\n    resolvedPagePath: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\api\\\\workflow\\\\stream\\\\[workflowId]\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_RoKey_App_rokey_app_src_app_api_workflow_stream_workflowId_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fworkflow%2Fstream%2F%5BworkflowId%5D%2Froute&page=%2Fapi%2Fworkflow%2Fstream%2F%5BworkflowId%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fworkflow%2Fstream%2F%5BworkflowId%5D%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/workflow/stream/[workflowId]/route.ts":
/*!***********************************************************!*\
  !*** ./src/app/api/workflow/stream/[workflowId]/route.ts ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   OPTIONS: () => (/* binding */ OPTIONS),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase/server */ \"(rsc)/./src/lib/supabase/server.ts\");\n/* harmony import */ var _lib_websocket_WorkflowWebSocketServer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/websocket/WorkflowWebSocketServer */ \"(rsc)/./src/lib/websocket/WorkflowWebSocketServer.ts\");\n/**\n * WebSocket API endpoint for real-time workflow updates\n * Provides Server-Sent Events stream for Manual Build workflow execution\n */ \n\n\nasync function GET(request, { params }) {\n    const { workflowId } = await params;\n    if (!workflowId) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Workflow ID is required'\n        }, {\n            status: 400\n        });\n    }\n    try {\n        // Authenticate user\n        const supabase = await (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createSupabaseServerClientOnRequest)();\n        const { data: { user }, error: authError } = await supabase.auth.getUser();\n        if (authError || !user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        // Verify user has access to this workflow\n        const { data: workflow, error: workflowError } = await supabase.from('manual_build_workflows').select('id, user_id, name, is_active').eq('id', workflowId).eq('user_id', user.id).single();\n        if (workflowError || !workflow) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Workflow not found or access denied'\n            }, {\n                status: 404\n            });\n        }\n        console.log(`[Workflow Stream] Starting stream for workflow ${workflowId} (${workflow.name})`);\n        // Create the event stream (this already sends connection_established event)\n        const stream = (0,_lib_websocket_WorkflowWebSocketServer__WEBPACK_IMPORTED_MODULE_2__.createWorkflowEventStream)(workflowId, user.id);\n        // Note: Connection established event is automatically sent by registerWorkflowConnection()\n        // No need to send it again here to avoid duplicate events\n        // Return SSE response\n        return new Response(stream, {\n            headers: {\n                'Content-Type': 'text/event-stream',\n                'Cache-Control': 'no-cache',\n                'Connection': 'keep-alive',\n                'Access-Control-Allow-Origin': '*',\n                'Access-Control-Allow-Headers': 'Cache-Control',\n                'X-Accel-Buffering': 'no'\n            }\n        });\n    } catch (error) {\n        console.error(`[Workflow Stream] Error: ${error}`);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\n/**\n * POST endpoint for sending workflow events (for testing or external triggers)\n */ async function POST(request, { params }) {\n    const { workflowId } = await params;\n    if (!workflowId) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Workflow ID is required'\n        }, {\n            status: 400\n        });\n    }\n    try {\n        // Authenticate user\n        const supabase = await (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createSupabaseServerClientOnRequest)();\n        const { data: { user }, error: authError } = await supabase.auth.getUser();\n        if (authError || !user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        // Parse request body\n        const body = await request.json();\n        const { eventType, data, executionId } = body;\n        if (!eventType) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Event type is required'\n            }, {\n                status: 400\n            });\n        }\n        // Verify user has access to this workflow\n        const { data: workflow, error: workflowError } = await supabase.from('manual_build_workflows').select('id, user_id').eq('id', workflowId).eq('user_id', user.id).single();\n        if (workflowError || !workflow) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Workflow not found or access denied'\n            }, {\n                status: 404\n            });\n        }\n        // Emit the event\n        (0,_lib_websocket_WorkflowWebSocketServer__WEBPACK_IMPORTED_MODULE_2__.emitWorkflowEvent)(workflowId, user.id, eventType, data, executionId);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'Event emitted successfully',\n            eventType,\n            workflowId\n        });\n    } catch (error) {\n        console.error(`[Workflow Stream] POST Error: ${error}`);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\n/**\n * OPTIONS endpoint for CORS preflight\n */ async function OPTIONS() {\n    return new Response(null, {\n        status: 200,\n        headers: {\n            'Access-Control-Allow-Origin': '*',\n            'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',\n            'Access-Control-Allow-Headers': 'Content-Type, Authorization'\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/workflow/stream/[workflowId]/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/server.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/server.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createServiceRoleClient: () => (/* binding */ createServiceRoleClient),\n/* harmony export */   createSupabaseServerClientFromRequest: () => (/* binding */ createSupabaseServerClientFromRequest),\n/* harmony export */   createSupabaseServerClientOnRequest: () => (/* binding */ createSupabaseServerClientOnRequest)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var _barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=createClient!=!@supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n\n\n\n// This is the standard setup for creating a Supabase server client\n// in Next.js App Router (Server Components, Route Handlers, Server Actions).\n// Updated for Next.js 15 async cookies requirement\nasync function createSupabaseServerClientOnRequest() {\n    const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8\", {\n        cookies: {\n            get (name) {\n                return cookieStore.get(name)?.value;\n            },\n            set (name, value, options) {\n                try {\n                    cookieStore.set({\n                        name,\n                        value,\n                        ...options\n                    });\n                } catch (error) {\n                    // This error can be ignored if running in a Server Component\n                    // where cookies can't be set directly. Cookie setting should be\n                    // handled in Server Actions or Route Handlers.\n                    console.warn(`Failed to set cookie '${name}' (might be in a Server Component):`, error);\n                }\n            },\n            remove (name, options) {\n                try {\n                    // To remove a cookie using the `set` method from `next/headers`,\n                    // you typically set it with an empty value and Max-Age=0 or an expiry date in the past.\n                    cookieStore.set({\n                        name,\n                        value: '',\n                        ...options\n                    });\n                } catch (error) {\n                    // Similar to set, this might fail in a Server Component.\n                    console.warn(`Failed to remove cookie '${name}' (might be in a Server Component):`, error);\n                }\n            }\n        }\n    });\n}\n// Alternative method for API routes that need to handle cookies from request\nfunction createSupabaseServerClientFromRequest(request) {\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8\", {\n        cookies: {\n            get (name) {\n                return request.cookies.get(name)?.value;\n            },\n            set (name, value, options) {\n            // In API routes, we can't set cookies directly on the request\n            // This will be handled by the response\n            },\n            remove (name, options) {\n            // In API routes, we can't remove cookies directly on the request\n            // This will be handled by the response\n            }\n        }\n    });\n}\n// Service role client for admin operations (OAuth token storage, etc.)\nfunction createServiceRoleClient() {\n    return (0,_barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__.createClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY, {\n        auth: {\n            autoRefreshToken: false,\n            persistSession: false\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/server.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/websocket/WorkflowWebSocketServer.ts":
/*!******************************************************!*\
  !*** ./src/lib/websocket/WorkflowWebSocketServer.ts ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   broadcastToConnection: () => (/* binding */ broadcastToConnection),\n/* harmony export */   broadcastToUser: () => (/* binding */ broadcastToUser),\n/* harmony export */   broadcastToWorkflow: () => (/* binding */ broadcastToWorkflow),\n/* harmony export */   cleanupInactiveConnections: () => (/* binding */ cleanupInactiveConnections),\n/* harmony export */   createWorkflowEventStream: () => (/* binding */ createWorkflowEventStream),\n/* harmony export */   emitWorkflowEvent: () => (/* binding */ emitWorkflowEvent),\n/* harmony export */   getActiveConnections: () => (/* binding */ getActiveConnections),\n/* harmony export */   getWorkflowConnectionsCount: () => (/* binding */ getWorkflowConnectionsCount),\n/* harmony export */   registerWorkflowConnection: () => (/* binding */ registerWorkflowConnection),\n/* harmony export */   unregisterWorkflowConnection: () => (/* binding */ unregisterWorkflowConnection)\n/* harmony export */ });\n/* harmony import */ var _barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! __barrel_optimize__?names=createClient!=!@supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n/**\n * WebSocket Server for Real-time Workflow Updates\n * Provides live updates during Manual Build workflow execution\n */ \n// In-memory storage for active WebSocket connections\nconst activeConnections = new Map();\n/**\n * Register a new WebSocket connection for workflow updates\n */ function registerWorkflowConnection(connectionId, workflowId, userId, controller) {\n    // Check if there are already too many connections for this workflow/user combo\n    const existingConnections = Array.from(activeConnections.values()).filter((conn)=>conn.workflowId === workflowId && conn.userId === userId);\n    if (existingConnections.length >= 3) {\n        console.warn(`[Workflow WebSocket] Too many connections (${existingConnections.length}) for workflow ${workflowId}, user ${userId}`);\n        // Close oldest connection\n        const oldestConnection = existingConnections.sort((a, b)=>new Date(a.connectedAt).getTime() - new Date(b.connectedAt).getTime())[0];\n        // Find and remove the oldest connection\n        for (const [connId, conn] of activeConnections.entries()){\n            if (conn === oldestConnection) {\n                unregisterWorkflowConnection(connId);\n                break;\n            }\n        }\n    }\n    const connection = {\n        workflowId,\n        userId,\n        controller,\n        connectedAt: new Date().toISOString()\n    };\n    activeConnections.set(connectionId, connection);\n    console.log(`[Workflow WebSocket] Registered connection ${connectionId} for workflow ${workflowId} (${activeConnections.size} total connections)`);\n    // Send initial connection event ONLY to this specific connection\n    const connectionEvent = {\n        id: crypto.randomUUID(),\n        workflowId,\n        type: 'connection_established',\n        timestamp: new Date().toISOString(),\n        data: {\n            message: '🔗 Connected to workflow real-time updates',\n            workflowId,\n            connectionId\n        },\n        userId\n    };\n    // Send only to this connection, not broadcast to all\n    broadcastToConnection(connectionId, connectionEvent);\n}\n/**\n * Unregister a WebSocket connection\n */ function unregisterWorkflowConnection(connectionId) {\n    const connection = activeConnections.get(connectionId);\n    if (connection) {\n        try {\n            // Try to close the controller if it's still active\n            connection.controller.close();\n        } catch (error) {\n        // Controller might already be closed, ignore error\n        }\n        activeConnections.delete(connectionId);\n        console.log(`[Workflow WebSocket] Unregistered connection ${connectionId} for workflow ${connection.workflowId} (${activeConnections.size} remaining)`);\n    }\n}\n/**\n * Broadcast event to a specific connection\n */ function broadcastToConnection(connectionId, event) {\n    const connection = activeConnections.get(connectionId);\n    if (connection) {\n        try {\n            const encoder = new TextEncoder();\n            const eventData = `id: ${event.id}\\nevent: ${event.type}\\ndata: ${JSON.stringify(event)}\\n\\n`;\n            connection.controller.enqueue(encoder.encode(eventData));\n            connection.lastEventId = event.id;\n            console.log(`[Workflow WebSocket] Sent ${event.type} to connection ${connectionId}`);\n        } catch (error) {\n            console.error(`[Workflow WebSocket] Error sending to connection ${connectionId}:`, error);\n            // Remove dead connection\n            activeConnections.delete(connectionId);\n        }\n    }\n}\n/**\n * Broadcast event to all connections for a specific workflow\n */ function broadcastToWorkflow(workflowId, event) {\n    const workflowConnections = Array.from(activeConnections.entries()).filter(([_, connection])=>connection.workflowId === workflowId);\n    workflowConnections.forEach(([connectionId, _])=>{\n        broadcastToConnection(connectionId, event);\n    });\n    console.log(`[Workflow WebSocket] Broadcasted ${event.type} to ${workflowConnections.length} connections for workflow ${workflowId}`);\n}\n/**\n * Broadcast event to all connections for a specific user\n */ function broadcastToUser(userId, event) {\n    const userConnections = Array.from(activeConnections.entries()).filter(([_, connection])=>connection.userId === userId);\n    userConnections.forEach(([connectionId, _])=>{\n        broadcastToConnection(connectionId, event);\n    });\n    console.log(`[Workflow WebSocket] Broadcasted ${event.type} to ${userConnections.length} connections for user ${userId}`);\n}\n/**\n * Get active connections count for a workflow\n */ function getWorkflowConnectionsCount(workflowId) {\n    return Array.from(activeConnections.values()).filter((connection)=>connection.workflowId === workflowId).length;\n}\n/**\n * Get all active connections for monitoring\n */ function getActiveConnections() {\n    return new Map(activeConnections);\n}\n/**\n * Emit workflow event to all relevant connections\n */ function emitWorkflowEvent(workflowId, userId, eventType, data, executionId) {\n    const event = {\n        id: crypto.randomUUID(),\n        workflowId,\n        executionId,\n        type: eventType,\n        timestamp: new Date().toISOString(),\n        data,\n        userId\n    };\n    // Broadcast to all connections for this workflow\n    broadcastToWorkflow(workflowId, event);\n    // Store event in database for persistence (optional) - skip connection events to avoid spam\n    if (eventType !== 'connection_established') {\n        storeWorkflowEvent(event).catch((error)=>{\n            console.error('[Workflow WebSocket] Failed to store event:', error);\n        });\n    }\n}\n/**\n * Store workflow event in database for persistence and replay\n */ async function storeWorkflowEvent(event) {\n    try {\n        const supabase = (0,_barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY);\n        await supabase.from('workflow_execution_logs').insert({\n            execution_id: event.executionId || event.workflowId,\n            workflow_id: event.workflowId,\n            node_id: event.data?.nodeId || 'system',\n            node_type: event.data?.nodeType || 'system',\n            log_level: event.type.includes('failed') ? 'error' : 'info',\n            message: event.data?.message || `Workflow event: ${event.type}`,\n            data: event.data,\n            duration_ms: event.data?.duration,\n            created_at: event.timestamp\n        });\n        console.log(`[Workflow WebSocket] Stored event ${event.type} for workflow ${event.workflowId}`);\n    } catch (error) {\n        console.error('[Workflow WebSocket] Failed to store event in database:', error);\n    }\n}\n/**\n * Create a Server-Sent Events stream for workflow updates\n */ function createWorkflowEventStream(workflowId, userId) {\n    const connectionId = crypto.randomUUID();\n    const encoder = new TextEncoder();\n    return new ReadableStream({\n        start (controller) {\n            registerWorkflowConnection(connectionId, workflowId, userId, controller);\n        },\n        cancel () {\n            unregisterWorkflowConnection(connectionId);\n            console.log(`[Workflow WebSocket] Stream cancelled for workflow ${workflowId}`);\n        }\n    });\n}\n/**\n * Cleanup inactive connections (called periodically)\n */ function cleanupInactiveConnections() {\n    const now = Date.now();\n    const maxAge = 30 * 60 * 1000; // 30 minutes\n    for (const [connectionId, connection] of activeConnections.entries()){\n        const connectionAge = now - new Date(connection.connectedAt).getTime();\n        if (connectionAge > maxAge) {\n            console.log(`[Workflow WebSocket] Cleaning up inactive connection ${connectionId}`);\n            unregisterWorkflowConnection(connectionId);\n        }\n    }\n}\n// Cleanup inactive connections every 5 minutes\nsetInterval(cleanupInactiveConnections, 5 * 60 * 1000);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/websocket/WorkflowWebSocketServer.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/cookie"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fworkflow%2Fstream%2F%5BworkflowId%5D%2Froute&page=%2Fapi%2Fworkflow%2Fstream%2F%5BworkflowId%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fworkflow%2Fstream%2F%5BworkflowId%5D%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();