'use client';

import React, { useState, useEffect, FormEvent, useRef, useCallback, useMemo, lazy, Suspense } from 'react';
import { type CustomApiConfig } from '@/types/customApiConfigs';
import { PaperClipIcon, XCircleIcon, PaperAirplaneIcon, TrashIcon, CheckIcon } from '@heroicons/react/24/solid';
import { PencilSquareIcon } from '@heroicons/react/24/outline';
import LazyMarkdownRenderer from '@/components/LazyMarkdownRenderer';
import CopyButton from '@/components/CopyButton';
import RetryDropdown from '@/components/RetryDropdown';
import DynamicStatusIndicator from '@/components/DynamicStatusIndicator';
import { MinimizedCanvasCard } from '@/components/MinimizedCanvasCard';

// Lazy load heavy components
const OrchestrationCanvas = lazy(() => import('@/components/OrchestrationCanvas').then(module => ({ default: module.OrchestrationCanvas })));
import { useSidebar } from '@/contexts/SidebarContext';
import { type ChatConversation, type ChatMessage, type NewChatConversation, type NewChatMessage } from '@/types/chatHistory';
// Temporarily comment out to fix import issue
// import { ChatHistorySkeleton, EnhancedChatHistorySkeleton, MessageSkeleton, ConfigSelectorSkeleton } from '@/components/LoadingSkeleton';
import { useChatHistory, useChatHistoryPrefetch } from '@/hooks/useChatHistory';
import { useSmartMessageStatus, logStatusPerformance } from '@/hooks/useMessageStatus';
import { useSubscription } from '@/hooks/useSubscription';
// import VirtualChatHistory from '@/components/VirtualChatHistory';

// Import performance logging utilities for browser console access
import '@/utils/performanceLogs';

// Performance monitoring
const trackPerformance = (operation: string, startTime: number) => {
  if (process.env.NODE_ENV === 'development') {
    const duration = performance.now() - startTime;
    console.log(`⚡ [PERFORMANCE] ${operation}: ${duration.toFixed(2)}ms`);
  }
};


// Updated PlaygroundMessage interface for multimodal content
interface PlaygroundMessageContentPartText {
  type: 'text';
  text: string;
}
interface PlaygroundMessageContentPartImage {
  type: 'image_url';
  image_url: { url: string }; // data URL for display and for sending (will be base64)
}
type PlaygroundMessageContentPart = PlaygroundMessageContentPartText | PlaygroundMessageContentPartImage;

// Memoized chat history item component to prevent unnecessary re-renders
const ChatHistoryItem = React.memo(({
  chat,
  currentConversation,
  onLoadChat,
  onDeleteChat
}: {
  chat: ChatConversation;
  currentConversation: ChatConversation | null;
  onLoadChat: (chat: ChatConversation) => void;
  onDeleteChat: (id: string) => void;
}) => {
  const isActive = currentConversation?.id === chat.id;

  return (
    <div
      className={`relative group p-3 hover:bg-white/10 rounded-xl transition-all duration-200 ${
        isActive ? 'bg-blue-500/20 border border-blue-400/30' : ''
      }`}
    >
      <button
        onClick={() => onLoadChat(chat)}
        className="w-full text-left"
      >
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <h4 className="text-sm font-medium text-white truncate mb-1">
              {chat.title}
            </h4>
            {chat.last_message_preview && (
              <p className="text-xs text-gray-400 line-clamp-2 mb-2">
                {chat.last_message_preview}
              </p>
            )}
            <div className="flex items-center justify-between text-xs text-gray-500">
              <span>{chat.message_count} messages</span>
              <span>{new Date(chat.updated_at).toLocaleDateString()}</span>
            </div>
          </div>
        </div>
      </button>

      <button
        onClick={(e) => {
          e.stopPropagation();
          onDeleteChat(chat.id);
        }}
        className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 p-1 text-gray-400 hover:text-red-400 hover:bg-red-500/20 rounded transition-all duration-200"
        title="Delete conversation"
      >
        <TrashIcon className="w-4 h-4" />
      </button>
    </div>
  );
});

// Add display name for debugging
ChatHistoryItem.displayName = 'ChatHistoryItem';

interface PlaygroundMessage {
  id: string;
  role: 'user' | 'assistant' | 'system' | 'error';
  content: PlaygroundMessageContentPart[]; // Always an array
}

export default function PlaygroundPage() {
  const { isCollapsed, isHovered, setHoverDisabled } = useSidebar();
  const { user } = useSubscription();



  // Calculate actual sidebar width (collapsed but can expand on hover)
  const sidebarWidth = (!isCollapsed || isHovered) ? '256px' : '64px';

  // Get user's first name for welcome message
  const firstName = user?.user_metadata?.first_name || user?.user_metadata?.full_name?.split(' ')[0] || '';

  const [customConfigs, setCustomConfigs] = useState<CustomApiConfig[]>([]);
  const [workflows, setWorkflows] = useState<any[]>([]);
  const [selectedConfigId, setSelectedConfigId] = useState<string>('');
  const [workflowStream, setWorkflowStream] = useState<EventSource | null>(null);
  const [workflowStatus, setWorkflowStatus] = useState<string>('');
  const [initialPageLoad, setInitialPageLoad] = useState(true);

  // Optimized API key prefetch - only when needed
  const prefetchApiKeys = useCallback(async (configId: string) => {
    if (!configId) return;

    // Skip prefetching for workflow IDs since they use a different API key system
    if (configId.startsWith('workflow_')) {
      if (process.env.NODE_ENV === 'development') {
        console.log('Skipping API key prefetch for workflow:', configId);
      }
      return;
    }

    try {
      const response = await fetch(`/api/keys?custom_config_id=${configId}`, {
        cache: 'force-cache', // Use browser cache for 5 minutes
        headers: {
          'Cache-Control': 'max-age=300'
        }
      });
      if (response.ok) {
        await response.json(); // Cache the response
      }
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.log('Background key prefetch failed:', error);
      }
    }
  }, []);

  // Prefetch API keys when config is selected (debounced)
  useEffect(() => {
    if (selectedConfigId) {
      const timer = setTimeout(() => {
        prefetchApiKeys(selectedConfigId);
      }, 1000); // Debounce to avoid excessive calls

      return () => clearTimeout(timer);
    }
  }, [selectedConfigId, prefetchApiKeys]);

  const [messageInput, setMessageInput] = useState<string>('');
  const [messages, setMessages] = useState<PlaygroundMessage[]>([]);

  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [useStreaming, setUseStreaming] = useState<boolean>(true);
  const [showScrollToBottom, setShowScrollToBottom] = useState(false);

  // New state for multiple image handling (up to 10 images)
  const [imageFiles, setImageFiles] = useState<File[]>([]);
  const [imagePreviews, setImagePreviews] = useState<string[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);

  // History sidebar state
  const [isHistoryCollapsed, setIsHistoryCollapsed] = useState<boolean>(false);
  const [currentConversation, setCurrentConversation] = useState<ChatConversation | null>(null);

  // Edit message state
  const [editingMessageId, setEditingMessageId] = useState<string | null>(null);
  const [editingText, setEditingText] = useState<string>('');
  const [isLoadingMessages, setIsLoadingMessages] = useState<boolean>(false);

  // Orchestration state
  const [orchestrationExecutionId, setOrchestrationExecutionId] = useState<string | null>(null);
  const [showOrchestration, setShowOrchestration] = useState<boolean>(false);
  const [orchestrationComplete, setOrchestrationComplete] = useState<boolean>(false);

  // Canvas state for split-screen layout
  const [isCanvasOpen, setIsCanvasOpen] = useState<boolean>(false);
  const [isCanvasMinimized, setIsCanvasMinimized] = useState<boolean>(false);
  const [triggerMaximize, setTriggerMaximize] = useState<boolean>(false);

  // Handle canvas state changes
  const handleCanvasStateChange = (canvasOpen: boolean, canvasMinimized: boolean) => {
    setIsCanvasOpen(canvasOpen);
    setIsCanvasMinimized(canvasMinimized);

    // Auto-minimize history sidebar when canvas opens
    if (canvasOpen && !canvasMinimized) {
      setIsHistoryCollapsed(true);
    }
  };

  // Disable sidebar hover when canvas is open
  useEffect(() => {
    setHoverDisabled(isCanvasOpen && !isCanvasMinimized);
  }, [isCanvasOpen, isCanvasMinimized, setHoverDisabled]);

  // Orchestration state tracking (production optimized)
  useEffect(() => {
    // Only log in development
    if (process.env.NODE_ENV === 'development') {
      console.log('🎭 [DEBUG] Orchestration state changed:', {
        showOrchestration,
        orchestrationExecutionId,
        isCanvasOpen,
        isCanvasMinimized,
        shouldRenderCanvas: showOrchestration && orchestrationExecutionId,
        timestamp: new Date().toISOString()
      });

      if (showOrchestration && orchestrationExecutionId) {
        console.log('🎭 [DEBUG] Canvas should be visible now!');
      }
    }
  }, [showOrchestration, orchestrationExecutionId, isCanvasOpen, isCanvasMinimized]);

  // Enhanced status tracking (production optimized)
  const messageStatus = useSmartMessageStatus({
    enableAutoProgression: true,
    onStageChange: process.env.NODE_ENV === 'development' ? (stage, timestamp) => {
      console.log(`🎯 Status: ${stage} at ${timestamp}`);
    } : undefined
  });

  // Orchestration status tracking
  const [orchestrationStatus, setOrchestrationStatus] = useState<string>('');

  // Function to update orchestration status based on streaming content
  const updateOrchestrationStatus = (deltaContent: string, messageStatusObj: any) => {
    let newStatus = '';

    if (deltaContent.includes('🎬 **Multi-Role AI Orchestration Started!**')) {
      newStatus = 'Multi-Role AI Orchestration Started';
    } else if (deltaContent.includes('📋 **Orchestration Plan:**')) {
      newStatus = 'Planning specialist assignments';
    } else if (deltaContent.includes('🤖 **Moderator:**')) {
      newStatus = 'Moderator coordinating specialists';
    } else if (deltaContent.includes('Specialist:') && deltaContent.includes('Working...')) {
      // Extract specialist name
      const specialistMatch = deltaContent.match(/(\w+)\s+Specialist:/);
      if (specialistMatch) {
        newStatus = `${specialistMatch[1]} Specialist working`;
      } else {
        newStatus = 'Specialist working on your request';
      }
    } else if (deltaContent.includes('🎭 **SYNTHESIS PHASE INITIATED**')) {
      newStatus = 'Synthesizing specialist responses';
    } else if (deltaContent.includes('Analyzing and processing')) {
      newStatus = 'Analyzing and processing with specialized expertise';
    }

    if (newStatus && newStatus !== orchestrationStatus) {
      console.log('🎭 Orchestration status update:', newStatus);
      setOrchestrationStatus(newStatus);
      messageStatusObj.updateOrchestrationStatus(newStatus);
    }
  };

  // Auto-continuation function for seamless multi-part responses
  const handleAutoContinuation = async () => {
    console.log('🔄 [AUTO-CONTINUE] Starting automatic continuation...');

    if (!selectedConfigId || !currentConversation) {
      console.error('🔄 [AUTO-CONTINUE] Missing config or conversation');
      return;
    }

    setIsLoading(true);
    setOrchestrationStatus('Continuing synthesis automatically...');
    messageStatus.startProcessing();

    try {
      // Create a continuation message
      const continuationMessage: PlaygroundMessage = {
        id: Date.now().toString() + '-continue',
        role: 'user',
        content: [{ type: 'text', text: 'continue' }],
      };

      // Add the continuation message to the UI
      setMessages(prevMessages => [...prevMessages, continuationMessage]);

      // Save continuation message to database
      await saveMessageToDatabase(currentConversation.id, continuationMessage);

      // Prepare payload for continuation
      const continuationPayload = {
        custom_api_config_id: selectedConfigId,
        messages: [
          ...messages.map(m => ({
            role: m.role,
            content: m.content.length === 1 && m.content[0].type === 'text'
              ? m.content[0].text
              : m.content
          })),
          { role: 'user', content: 'continue' }
        ],
        stream: useStreaming,
        // Include user ID for internal playground requests
        ...(user?.id && { _internal_user_id: user.id }),
      };

      // Make the continuation request
      const response = await fetch('/api/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.NEXT_PUBLIC_ROKEY_API_ACCESS_TOKEN || 'Y21ErgiIHrVgoSidqkShBT9npjOuP8fAlzQlTVfkmaeUUY4Kpq%*13'}`,
        },
        body: JSON.stringify(continuationPayload),
        cache: 'no-store',
      });

      // Check for synthesis completion response
      if (response.ok) {
        // Check if this is a synthesis completion response
        const responseText = await response.text();
        let responseData;

        try {
          responseData = JSON.parse(responseText);
        } catch {
          // If it's not JSON, treat as regular response
          responseData = null;
        }

        // Handle synthesis completion
        if (responseData?.error === 'synthesis_complete') {
          console.log('🎉 [AUTO-CONTINUE] Synthesis is complete! Treating "continue" as new conversation.');

          // Remove the continuation message we just added
          setMessages(prevMessages => prevMessages.slice(0, -1));

          // Clear the loading state
          setIsLoading(false);
          setOrchestrationStatus('');
          messageStatus.markComplete();

          // Process the "continue" as a new message by calling the normal send flow
          // But first we need to set the input back to "continue"
          setMessageInput('continue');

          // Call the normal send message flow which will handle it as a new conversation
          setTimeout(() => {
            handleSendMessage();
          }, 100);
          return;
        }

        // If not synthesis completion, recreate the response for normal processing
        const recreatedResponse = new Response(responseText, {
          status: response.status,
          statusText: response.statusText,
          headers: response.headers
        });

        // Handle the continuation response
        if (useStreaming && recreatedResponse.body) {
          const reader = recreatedResponse.body.getReader();
        const decoder = new TextDecoder();
        const assistantMessageId = Date.now().toString() + '-assistant-continue';
        const currentAssistantMessage: PlaygroundMessage = {
          id: assistantMessageId,
          role: 'assistant',
          content: [{ type: 'text', text: '' }],
        };
        setMessages(prevMessages => [...prevMessages, currentAssistantMessage]);

        let accumulatedText = '';
        let isOrchestrationDetected = false;
        let streamingStatusTimeout: NodeJS.Timeout | null = null;

        // Check response headers to determine if this is chunked synthesis continuation
        const synthesisProgress = recreatedResponse.headers.get('X-Synthesis-Progress');
        const synthesisComplete = recreatedResponse.headers.get('X-Synthesis-Complete');
        const isChunkedSynthesis = synthesisProgress !== null;

        if (isChunkedSynthesis) {
          console.log('🔄 [AUTO-CONTINUE] Detected chunked synthesis continuation');
          messageStatus.markStreaming();
          setOrchestrationStatus('');
        } else {
          // Start with continuation status, but allow orchestration detection to override
          messageStatus.markOrchestrationStarted();
          setOrchestrationStatus('Continuing synthesis...');

          // Set up delayed streaming status, but allow orchestration detection to override
          streamingStatusTimeout = setTimeout(() => {
            if (!isOrchestrationDetected) {
              console.log('🎯 [AUTO-CONTINUE] No orchestration detected - switching to typing status');
              messageStatus.markStreaming();
              setOrchestrationStatus('');
            }
          }, 800);
        }

        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          const chunk = decoder.decode(value, { stream: true });
          const lines = chunk.split('\n');

          for (const line of lines) {
            if (line.startsWith('data: ')) {
              const jsonData = line.substring(6);
              if (jsonData.trim() === '[DONE]') break;

              try {
                const parsedChunk = JSON.parse(jsonData);
                if (parsedChunk.choices && parsedChunk.choices[0]?.delta?.content) {
                  const deltaContent = parsedChunk.choices[0].delta.content;
                  accumulatedText += deltaContent;

                  // Only check for orchestration if this is NOT a chunked synthesis continuation
                  if (!isChunkedSynthesis && !isOrchestrationDetected && (
                      deltaContent.includes('🎬 **Multi-Role AI Orchestration Started!**') ||
                      deltaContent.includes('📋 **Orchestration Plan:**') ||
                      deltaContent.includes('🎭 **SYNTHESIS PHASE INITIATED**') ||
                      deltaContent.includes('🤖 **Moderator:**') ||
                      deltaContent.includes('Specialist:')
                  )) {
                    console.log('🎭 [AUTO-CONTINUE] Detected NEW orchestration - this should be direct continuation instead');
                    isOrchestrationDetected = true;

                    // Cancel the delayed streaming status
                    if (streamingStatusTimeout) {
                      clearTimeout(streamingStatusTimeout);
                      streamingStatusTimeout = null;
                    }

                    // Update orchestration status for new orchestration
                    updateOrchestrationStatus(deltaContent, messageStatus);
                  } else if (!isChunkedSynthesis && isOrchestrationDetected) {
                    // Continue updating orchestration status if already detected
                    updateOrchestrationStatus(deltaContent, messageStatus);
                  } else {
                    // This is direct continuation content (chunked synthesis or regular continuation)
                    // Keep the current status without changing it
                  }

                  const textContent = currentAssistantMessage.content[0] as PlaygroundMessageContentPartText;
                  textContent.text = accumulatedText;
                  setMessages(prevMessages => prevMessages.map(msg =>
                    msg.id === assistantMessageId ? { ...msg, content: [textContent] } : msg
                  ));
                }
              } catch (parseError) {
                console.warn('Auto-continuation: Failed to parse stream chunk JSON:', jsonData, parseError);
              }
            }
          }
        }

        // Clean up timeout if still pending
        if (streamingStatusTimeout) {
          clearTimeout(streamingStatusTimeout);
        }

        // Save the continuation response
        if (accumulatedText) {
          const finalContinuationMessage: PlaygroundMessage = {
            ...currentAssistantMessage,
            content: [{ type: 'text', text: accumulatedText }]
          };

          // Check if we need auto-continuation for chunked synthesis
          const needsAutoContinuation = (
            isChunkedSynthesis &&
            synthesisComplete !== 'true' &&
            accumulatedText.includes('[SYNTHESIS CONTINUES AUTOMATICALLY...]')
          );

          if (needsAutoContinuation) {
            console.log('🔄 [AUTO-CONTINUE] Detected chunked synthesis continuation, starting next chunk...');

            // Save current message first
            await saveMessageToDatabase(currentConversation.id, finalContinuationMessage);

            // Start auto-continuation after a brief delay
            setTimeout(() => {
              handleAutoContinuation();
            }, 1000);
          } else {
            await saveMessageToDatabase(currentConversation.id, finalContinuationMessage);
          }
        }
        }
      } else {
        // Handle non-ok response
        throw new Error(`Auto-continuation failed: ${response.status}`);
      }

    } catch (error) {
      console.error('🔄 [AUTO-CONTINUE] Error:', error);
      const errorMessage: PlaygroundMessage = {
        id: Date.now().toString() + '-error-continue',
        role: 'error',
        content: [{ type: 'text', text: `Auto-continuation failed: ${error instanceof Error ? error.message : 'Unknown error'}` }],
      };
      setMessages(prevMessages => [...prevMessages, errorMessage]);
    } finally {
      setIsLoading(false);
      setOrchestrationStatus('');
      messageStatus.markComplete();
    }
  };

  // Enhanced chat history with optimized caching
  const {
    chatHistory,
    isLoading: isLoadingHistory,
    isStale: isChatHistoryStale,
    error: chatHistoryError,
    refetch: refetchChatHistory,
    prefetch: prefetchChatHistory,
    invalidateCache: invalidateChatHistoryCache
  } = useChatHistory({
    configId: selectedConfigId,
    enablePrefetch: true,
    cacheTimeout: 300000, // 5 minutes
    staleTimeout: 30000   // 30 seconds - show stale data while fetching fresh
  });

  // Chat history prefetching hook
  const { prefetchChatHistory: prefetchForNavigation } = useChatHistoryPrefetch();





  // Conversation starters (memoized to prevent re-creation)
  const conversationStarters = useMemo(() => [
    {
      id: 'explain-concept',
      title: 'Explain a concept',
      description: 'Get clear explanations on any topic',
      icon: '💡',
      color: 'bg-blue-100 text-blue-700',
      prompt: 'Explain how machine learning works in simple terms'
    },
    {
      id: 'write-code',
      title: 'Write code',
      description: 'Generate code snippets and solutions',
      icon: '💻',
      color: 'bg-green-100 text-green-700',
      prompt: 'Write a Python function that calculates the fibonacci sequence'
    },
    {
      id: 'brainstorm-ideas',
      title: 'Brainstorm ideas',
      description: 'Generate creative solutions and ideas',
      icon: '🧠',
      color: 'bg-purple-100 text-purple-700',
      prompt: 'Help me brainstorm innovative features for a mobile app'
    },
    {
      id: 'analyze-data',
      title: 'Analyze data',
      description: 'Get insights from data and trends',
      icon: '📊',
      color: 'bg-amber-100 text-amber-700',
      prompt: 'What are the key trends in artificial intelligence for 2025?'
    }
  ], []);

  // Fetch Custom API Configs and Workflows for the dropdown with progressive loading
  useEffect(() => {
    const fetchConfigs = async () => {
      const startTime = performance.now();
      try {
        // Progressive loading: render UI first, then load configs
        if (initialPageLoad) {
          await new Promise(resolve => setTimeout(resolve, 50));
        }

        const response = await fetch('/api/custom-configs');
        if (!response.ok) {
          const errData = await response.json();
          throw new Error(errData.error || 'Failed to fetch configurations');
        }

        const data: CustomApiConfig[] = await response.json();
        setCustomConfigs(data);

        // Check for workflow URL parameter first
        const urlParams = new URLSearchParams(window.location.search);
        const workflowParam = urlParams.get('workflow');

        if (workflowParam) {
          // Pre-select workflow from URL parameter
          setSelectedConfigId(`workflow_${workflowParam}`);
          console.log(`🎯 Pre-selecting workflow from URL: ${workflowParam}`);
        } else if (data.length > 0) {
          setSelectedConfigId(data[0].id);
        }

        setInitialPageLoad(false);
        trackPerformance('Config fetch', startTime);
      } catch (err: any) {
        setError(`Failed to load configurations: ${err.message}`);
        setCustomConfigs([]);
        setInitialPageLoad(false);
        trackPerformance('Config fetch (failed)', startTime);
      }
    };

    const fetchWorkflows = async () => {
      try {
        const response = await fetch('/api/workflows');
        if (response.ok) {
          const data = await response.json();
          setWorkflows(data.workflows || []);
        }
      } catch (err: any) {
        console.error('Failed to fetch workflows:', err);
        setWorkflows([]);
      }
    };

    // Only fetch configs and workflows when user is authenticated
    if (user && initialPageLoad) {
      fetchConfigs();
      fetchWorkflows();
    }
  }, [initialPageLoad, user]); // Add user dependency

  // Cleanup workflow stream on unmount or config change
  useEffect(() => {
    return () => {
      if (workflowStream) {
        workflowStream.close();
        setWorkflowStream(null);
        setWorkflowStatus('');
      }
    };
  }, [selectedConfigId]);

  // Cleanup workflow stream on component unmount
  useEffect(() => {
    return () => {
      if (workflowStream) {
        workflowStream.close();
      }
    };
  }, []);



  // Helper function to convert File to base64
  const fileToBase64 = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = (error) => reject(error);
    });
  };

  const handleImageChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    if (files.length === 0) return;

    // Limit to 10 images total
    const currentCount = imageFiles.length;
    const availableSlots = 10 - currentCount;
    const filesToAdd = files.slice(0, availableSlots);

    if (filesToAdd.length < files.length) {
      setError(`You can only upload up to 10 images. ${files.length - filesToAdd.length} images were not added.`);
    }

    try {
      const newPreviews: string[] = [];
      for (const file of filesToAdd) {
        const previewUrl = await fileToBase64(file);
        newPreviews.push(previewUrl);
      }

      setImageFiles(prev => [...prev, ...filesToAdd]);
      setImagePreviews(prev => [...prev, ...newPreviews]);
    } catch (error) {
      console.error('Error processing images:', error);
      setError('Failed to process one or more images. Please try again.');
    }

    // Reset file input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleRemoveImage = (index?: number) => {
    if (index !== undefined) {
      // Remove specific image
      setImageFiles(prev => prev.filter((_, i) => i !== index));
      setImagePreviews(prev => prev.filter((_, i) => i !== index));
    } else {
      // Remove all images
      setImageFiles([]);
      setImagePreviews([]);
    }

    if (fileInputRef.current) {
      fileInputRef.current.value = ''; // Reset file input
    }
  };

  // Scroll management functions
  const scrollToBottom = (smooth: boolean = false) => {
    if (messagesContainerRef.current) {
      messagesContainerRef.current.scrollTo({
        top: messagesContainerRef.current.scrollHeight,
        behavior: smooth ? 'smooth' : 'auto'
      });
    }
  };

  // Auto-scroll to position new user message near top (like starting fresh conversation)
  const scrollToNewMessage = (smooth: boolean = true) => {
    console.log('📍 scrollToNewMessage called, container ref:', messagesContainerRef.current);

    if (messagesContainerRef.current) {
      const container = messagesContainerRef.current;

      // Try multiple approaches to ensure scrolling works
      setTimeout(() => {
        const containerHeight = container.clientHeight;
        const scrollHeight = container.scrollHeight;

        console.log('📍 Container dimensions:', {
          containerHeight,
          scrollHeight,
          currentScrollTop: container.scrollTop
        });

        // Method 1: Calculate position to show new message near top
        const offsetFromBottom = containerHeight * 0.8; // Show message 20% from top
        const targetPosition = scrollHeight - offsetFromBottom;

        console.log('📍 Attempting scroll to position:', targetPosition);

        // Try scrollTo first
        container.scrollTo({
          top: targetPosition,
          behavior: 'smooth'
        });

        // Method 2: Also try setting scrollTop directly as backup
        setTimeout(() => {
          if (Math.abs(container.scrollTop - targetPosition) > 50) {
            console.log('📍 ScrollTo failed, trying direct scrollTop assignment');
            container.scrollTop = targetPosition;
          }
        }, 100);

        // Method 3: Find and scroll to the last message element
        setTimeout(() => {
          const messageElements = container.querySelectorAll('[data-message-id]');
          const lastMessageElement = messageElements[messageElements.length - 1] as HTMLElement;

          if (lastMessageElement) {
            console.log('📍 Trying scrollIntoView on last message');
            lastMessageElement.scrollIntoView({
              behavior: 'smooth',
              block: 'start',
              inline: 'nearest'
            });
          }
        }, 200);

      }, 100);
    } else {
      console.error('❌ messagesContainerRef.current is null!');
    }
  };

  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const container = e.currentTarget;
    const isNearBottom = container.scrollHeight - container.scrollTop - container.clientHeight < 100;
    setShowScrollToBottom(!isNearBottom && messages.length > 0);
  };

  // Virtual scrolling optimization - only render visible messages
  const visibleMessages = useMemo(() => {
    // For performance, only render last 50 messages if there are more than 100
    if (messages.length > 100) {
      return messages.slice(-50);
    }
    return messages;
  }, [messages]);

  // Auto-scroll when new messages are added
  useEffect(() => {
    if (messages.length > 0) {
      const lastMessage = messages[messages.length - 1];

      console.log('🔄 Auto-scroll triggered:', {
        messageCount: messages.length,
        lastMessageRole: lastMessage?.role,
        lastMessageId: lastMessage?.id
      });

      // Use requestAnimationFrame to ensure DOM has updated
      requestAnimationFrame(() => {
        if (lastMessage?.role === 'user') {
          console.log('📍 Scrolling to new user message position');
          // For user messages, scroll to position them near the top (like fresh conversation)
          scrollToNewMessage();

          // Fallback: if custom scroll doesn't work, at least scroll to bottom
          setTimeout(() => {
            if (messagesContainerRef.current) {
              const container = messagesContainerRef.current;
              const isNearBottom = container.scrollHeight - container.scrollTop - container.clientHeight < 200;
              if (!isNearBottom) {
                console.log('📍 Fallback: scrolling to bottom');
                scrollToBottom(true);
              }
            }
          }, 300);
        } else {
          console.log('📍 Scrolling to bottom for assistant message');
          // For assistant messages and others, scroll to bottom as usual
          scrollToBottom();
        }
      });
    }
  }, [messages.length]);

  // Auto-scroll during streaming responses
  useEffect(() => {
    if (isLoading && messages.length > 0) {
      // Scroll to bottom during streaming to show new content
      requestAnimationFrame(() => {
        scrollToBottom();
      });
    }
  }, [messages, isLoading]);

  // Auto-scroll when streaming content updates
  useEffect(() => {
    if (isLoading && messages.length > 0) {
      const lastMessage = messages[messages.length - 1];
      if (lastMessage && lastMessage.role === 'assistant') {
        // Scroll to bottom when assistant message content updates during streaming
        requestAnimationFrame(() => {
          scrollToBottom();
        });
      }
    }
  }, [messages, isLoading]);

  // Handle sidebar state changes to ensure proper centering
  useEffect(() => {
    // Small delay to allow CSS transitions to complete
    const timer = setTimeout(() => {
      if (messages.length > 0) {
        // Maintain scroll position when sidebar toggles
        requestAnimationFrame(() => {
          if (messagesContainerRef.current) {
            const container = messagesContainerRef.current;
            const isNearBottom = container.scrollHeight - container.scrollTop - container.clientHeight < 100;
            if (isNearBottom) {
              scrollToBottom();
            }
          }
        });
      }
    }, 200); // Match the transition duration

    return () => clearTimeout(timer);
  }, [isCollapsed, isHovered, isHistoryCollapsed, messages.length]);

  // Prefetch chat history when hovering over configs
  useEffect(() => {
    if (selectedConfigId && customConfigs.length > 0) {
      // Prefetch chat history for other configs when user is idle
      const otherConfigs = customConfigs
        .filter(config => config.id !== selectedConfigId)
        .slice(0, 3); // Limit to 3 most recent other configs

      const timer = setTimeout(() => {
        otherConfigs.forEach(config => {
          prefetchForNavigation(config.id);
        });
      }, 2000); // Wait 2 seconds before prefetching

      return () => clearTimeout(timer);
    }
  }, [selectedConfigId, customConfigs, prefetchForNavigation]);

  // Load messages for a specific conversation with pagination
  const loadConversation = async (conversation: ChatConversation, loadMore = false) => {
    // Set loading state for message loading
    if (!loadMore) {
      setIsLoadingMessages(true);
    }

    try {
      // Note: isLoadingHistory is now managed by the useChatHistory hook

      // Phase 1 Optimization: Reduced from 50 to 25 messages for faster loading
      // For initial load, get latest 25 messages
      // For load more, get older messages with offset
      const limit = 25;
      const offset = loadMore ? messages.length : 0;
      const latest = !loadMore;

      // Add cache-busting parameter to ensure fresh data after edits
      const cacheBuster = Date.now();
      const response = await fetch(
        `/api/chat/messages?conversation_id=${conversation.id}&limit=${limit}&offset=${offset}&latest=${latest}&_cb=${cacheBuster}`,
        {
          cache: 'no-store', // Ensure we don't use cached responses
          headers: {
            'Cache-Control': 'no-cache'
          }
        }
      );

      if (!response.ok) {
        throw new Error('Failed to load conversation messages');
      }
      const chatMessages: ChatMessage[] = await response.json();

      // Convert ChatMessage to PlaygroundMessage format
      const playgroundMessages: PlaygroundMessage[] = chatMessages.map(msg => ({
        id: msg.id,
        role: msg.role as 'user' | 'assistant' | 'system' | 'error',
        content: msg.content.map(part => {
          if (part.type === 'text' && part.text) {
            return { type: 'text', text: part.text } as PlaygroundMessageContentPartText;
          } else if (part.type === 'image_url' && part.image_url?.url) {
            return { type: 'image_url', image_url: { url: part.image_url.url } } as PlaygroundMessageContentPartImage;
          } else {
            // Fallback for malformed content
            return { type: 'text', text: '' } as PlaygroundMessageContentPartText;
          }
        }),
      }));

      if (loadMore) {
        // Prepend older messages to the beginning
        setMessages(prev => [...playgroundMessages, ...prev]);
      } else {
        // Replace all messages for initial load
        setMessages(playgroundMessages);
        // Note: currentConversation is now set optimistically in loadChatFromHistory
        // Only set it here if it's not already set (for direct loadConversation calls)
        if (!currentConversation || currentConversation.id !== conversation.id) {
          setCurrentConversation(conversation);
        }
      }
      setError(null);
    } catch (err: any) {
      console.error('Error loading conversation:', err);
      setError(`Failed to load conversation: ${err.message}`);
    } finally {
      // Clear loading state for message loading
      if (!loadMore) {
        setIsLoadingMessages(false);
      }
      // Note: isLoadingHistory is now managed by the useChatHistory hook
    }
  };

  // Save current conversation
  const saveConversation = async () => {
    if (!selectedConfigId || messages.length === 0) return null;

    try {
      let conversationId = currentConversation?.id;

      // Create new conversation if none exists
      if (!conversationId) {
        const firstMessage = messages[0];
        let title = 'New Chat';

        if (firstMessage && firstMessage.content.length > 0) {
          const textPart = firstMessage.content.find(part => part.type === 'text');
          if (textPart && textPart.text) {
            title = textPart.text.slice(0, 50) + (textPart.text.length > 50 ? '...' : '');
          }
        }

        // Determine if this is a workflow or router configuration
        const isWorkflow = selectedConfigId.startsWith('workflow_');
        const actualId = isWorkflow ? selectedConfigId.replace('workflow_', '') : selectedConfigId;

        const newConversationData: NewChatConversation = {
          title,
        };

        if (isWorkflow) {
          newConversationData.workflow_id = actualId;
        } else {
          newConversationData.custom_api_config_id = actualId;
        }

        const response = await fetch('/api/chat/conversations', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(newConversationData),
        });

        if (!response.ok) {
          throw new Error('Failed to create conversation');
        }

        const newConversation: ChatConversation = await response.json();
        conversationId = newConversation.id;
        setCurrentConversation(newConversation);
      }

      // Save all messages that aren't already saved
      for (const message of messages) {
        // Check if message is already saved (has UUID format)
        if (message.id.includes('-') && message.id.length > 20) continue;

        const newMessageData: NewChatMessage = {
          conversation_id: conversationId,
          role: message.role,
          content: message.content,
        };

        await fetch('/api/chat/messages', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(newMessageData),
        });
      }

      // Only refresh chat history if we created a new conversation
      if (!currentConversation) {
        refetchChatHistory(true); // Force refresh for new conversations
      }
      return conversationId;
    } catch (err: any) {
      console.error('Error saving conversation:', err);
      setError(`Failed to save conversation: ${err.message}`);
      return null;
    }
  };

  // Delete a conversation
  const deleteConversation = async (conversationId: string) => {
    try {
      const response = await fetch(`/api/chat/conversations?id=${conversationId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete conversation');
      }

      // If this was the current conversation, clear it
      if (currentConversation?.id === conversationId) {
        setCurrentConversation(null);
        setMessages([]);
      }

      // Force refresh chat history after deletion
      refetchChatHistory(true);
    } catch (err: any) {
      console.error('Error deleting conversation:', err);
      setError(`Failed to delete conversation: ${err.message}`);
    }
  };

  // Create a new conversation automatically when first message is sent
  const createNewConversation = async (firstMessage: PlaygroundMessage): Promise<string | null> => {
    if (!selectedConfigId) return null;

    try {
      // Generate title from first message
      let title = 'New Chat';
      if (firstMessage.content.length > 0) {
        const textPart = firstMessage.content.find(part => part.type === 'text');
        if (textPart && textPart.text) {
          title = textPart.text.slice(0, 50) + (textPart.text.length > 50 ? '...' : '');
        }
      }

      // Determine if this is a workflow or router configuration
      const isWorkflow = selectedConfigId.startsWith('workflow_');
      const actualId = isWorkflow ? selectedConfigId.replace('workflow_', '') : selectedConfigId;

      const newConversationData: NewChatConversation = {
        title,
      };

      if (isWorkflow) {
        newConversationData.workflow_id = actualId;
      } else {
        newConversationData.custom_api_config_id = actualId;
      }

      const response = await fetch('/api/chat/conversations', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(newConversationData),
      });

      if (!response.ok) {
        throw new Error('Failed to create conversation');
      }

      const newConversation: ChatConversation = await response.json();
      setCurrentConversation(newConversation);
      return newConversation.id;
    } catch (err: any) {
      console.error('Error creating conversation:', err);
      setError(`Failed to create conversation: ${err.message}`);
      return null;
    }
  };

  // Save individual message to database
  const saveMessageToDatabase = async (conversationId: string, message: PlaygroundMessage) => {
    try {
      const newMessageData: NewChatMessage = {
        conversation_id: conversationId,
        role: message.role,
        content: message.content,
      };

      const response = await fetch('/api/chat/messages', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(newMessageData),
      });

      if (!response.ok) {
        throw new Error('Failed to save message');
      }

      return await response.json();
    } catch (err: any) {
      console.error('Error saving message:', err);
      // Don't show error to user for message saving failures
      // The conversation will still work in the UI
    }
  };

  const handleStarterClick = (prompt: string) => {
    setMessageInput(prompt);
    // Auto-focus the input after setting the prompt
    setTimeout(() => {
      const textarea = document.querySelector('textarea[placeholder*="Type a message"]') as HTMLTextAreaElement;
      if (textarea) {
        textarea.focus();
        textarea.setSelectionRange(textarea.value.length, textarea.value.length);
      }
    }, 100);
  };

  const startNewChat = async () => {
    // Save current conversation if it has messages
    if (messages.length > 0) {
      await saveConversation();
    }

    setMessages([]);
    setCurrentConversation(null);
    setMessageInput('');
    setError(null);
    handleRemoveImage();
    // Reset status tracking
    messageStatus.reset();
  };

  // Handle model/router configuration or workflow change
  const handleConfigChange = useCallback(async (newConfigId: string) => {
    // Don't do anything if it's the same config
    if (newConfigId === selectedConfigId) return;

    // If there's an existing conversation with messages, start a new chat
    if (messages.length > 0) {
      if (process.env.NODE_ENV === 'development') {
        console.log('🔄 [Model Switch] Starting new chat due to model change');
      }
      await startNewChat();
    }

    // Update the selected configuration
    setSelectedConfigId(newConfigId);

    // Find the config or workflow name for logging
    let configName = newConfigId;
    if (newConfigId.startsWith('workflow_')) {
      const workflowId = newConfigId.replace('workflow_', '');
      const selectedWorkflow = workflows.find(w => w.id === workflowId);
      configName = selectedWorkflow ? `Workflow: ${selectedWorkflow.name}` : newConfigId;
    } else {
      const selectedConfig = customConfigs.find(config => config.id === newConfigId);
      configName = selectedConfig ? selectedConfig.name : newConfigId;
    }

    if (process.env.NODE_ENV === 'development') {
      console.log(`🔄 [Model Switch] Switched to: ${configName} (${newConfigId})`);
    }
  }, [selectedConfigId, messages.length, startNewChat, setSelectedConfigId, customConfigs, workflows]);

  const loadChatFromHistory = async (conversation: ChatConversation) => {
    // Optimistic UI update - immediately switch to the selected conversation
    console.log(`🔄 [INSTANT SWITCH] Immediately switching to conversation: ${conversation.title}`);

    // Clear current state immediately for instant feedback
    setCurrentConversation(conversation);
    setMessages([]); // Clear messages immediately to show loading state
    setMessageInput('');
    setError(null);
    handleRemoveImage();

    // Save current conversation in background (non-blocking)
    const savePromise = (async () => {
      if (messages.length > 0 && !currentConversation) {
        try {
          await saveConversation();
        } catch (err) {
          console.error('Background save failed:', err);
        }
      }
    })();

    // Load conversation messages in background
    try {
      await loadConversation(conversation);
      console.log(`✅ [INSTANT SWITCH] Successfully loaded conversation: ${conversation.title}`);
    } catch (err: any) {
      console.error('Error loading conversation:', err);
      setError(`Failed to load conversation: ${err.message}`);
      // Don't revert currentConversation - keep the UI showing the selected conversation
    }

    // Ensure background save completes
    await savePromise;
  };

  // Edit message functionality
  const startEditingMessage = (messageId: string, currentText: string) => {
    setEditingMessageId(messageId);
    setEditingText(currentText);
  };

  const cancelEditingMessage = () => {
    setEditingMessageId(null);
    setEditingText('');
  };

  const saveEditedMessage = async () => {
    if (!editingMessageId || !editingText.trim() || !selectedConfigId) return;

    // Find the index of the message being edited
    const messageIndex = messages.findIndex(msg => msg.id === editingMessageId);
    if (messageIndex === -1) return;

    // Update the message content
    const updatedMessages = [...messages];
    updatedMessages[messageIndex] = {
      ...updatedMessages[messageIndex],
      content: [{ type: 'text', text: editingText.trim() }]
    };

    // Remove all messages after the edited message (restart conversation from this point)
    const messagesToKeep = updatedMessages.slice(0, messageIndex + 1);

    setMessages(messagesToKeep);
    setEditingMessageId(null);
    setEditingText('');

    // If we have a current conversation, update the database
    if (currentConversation) {
      try {
        // Delete messages after the edited one from the database
        const messagesToDelete = messages.slice(messageIndex + 1);
        console.log(`🗑️ [EDIT MODE] Deleting ${messagesToDelete.length} messages after edited message`);

        // Delete messages after the edited message from the database
        if (messagesToDelete.length > 0) {
          const editedMessage = messages[messageIndex];

          // Check if the edited message is from database (UUID) or new (timestamp)
          const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
          const isEditedMessageFromDB = uuidRegex.test(editedMessage.id);

          if (isEditedMessageFromDB) {
            // For database messages, delete by created_at timestamp
            // Get the created_at timestamp from the database message
            const messageResponse = await fetch(`/api/chat/messages?conversation_id=${currentConversation.id}&limit=1&latest=false`);
            if (messageResponse.ok) {
              const allMessages = await messageResponse.json();
              const dbMessage = allMessages.find((msg: any) => msg.id === editedMessage.id);
              if (dbMessage) {
                const editedMessageTimestamp = new Date(dbMessage.created_at).getTime();
                console.log(`🗑️ [EDIT MODE] Deleting all messages after database timestamp: ${editedMessageTimestamp}`);

                const deleteResponse = await fetch(`/api/chat/messages/delete-after-timestamp`, {
                  method: 'DELETE',
                  headers: { 'Content-Type': 'application/json' },
                  body: JSON.stringify({
                    conversation_id: currentConversation.id,
                    after_timestamp: editedMessageTimestamp
                  }),
                });

                if (!deleteResponse.ok) {
                  console.error(`Failed to delete messages after timestamp:`, await deleteResponse.text());
                } else {
                  const result = await deleteResponse.json();
                  console.log(`✅ [EDIT MODE] Successfully deleted ${result.deleted_count} messages`);
                }
              }
            }
          } else {
            // For new messages with timestamp IDs, use the timestamp directly
            const editedMessageTimestamp = parseInt(editedMessage.id) || Date.now();
            console.log(`🗑️ [EDIT MODE] Deleting all messages after new message timestamp: ${editedMessageTimestamp}`);

            const deleteResponse = await fetch(`/api/chat/messages/delete-after-timestamp`, {
              method: 'DELETE',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                conversation_id: currentConversation.id,
                after_timestamp: editedMessageTimestamp
              }),
            });

            if (!deleteResponse.ok) {
              console.error(`Failed to delete messages after timestamp:`, await deleteResponse.text());
            } else {
              const result = await deleteResponse.json();
              console.log(`✅ [EDIT MODE] Successfully deleted ${result.deleted_count} messages`);
            }
          }
        }

        // Update/save the edited message in the database
        const editedMessage = messagesToKeep[messageIndex];
        console.log(`✏️ [EDIT MODE] Updating edited message with ID: ${editedMessage.id}`);

        // Check if this is a database message (UUID format) or a new message (timestamp format)
        // UUID format: xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx (36 characters with 4 hyphens)
        const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
        const isUUID = uuidRegex.test(editedMessage.id);

        console.log(`🔍 [EDIT MODE] Message ID: "${editedMessage.id}", isUUID: ${isUUID}, length: ${editedMessage.id.length}`);

        if (isUUID) {
          // Use direct ID-based update for database messages
          const updateResponse = await fetch(`/api/chat/messages?id=${editedMessage.id}`, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              content: editedMessage.content // This is already in the correct array format
            }),
          });

          if (!updateResponse.ok) {
            const errorText = await updateResponse.text();
            console.error(`Failed to update message by ID:`, errorText);
            throw new Error(`Failed to update message: ${errorText}`);
          } else {
            const result = await updateResponse.json();
            console.log(`✅ [EDIT MODE] Successfully updated message: ${result.id}`);
          }
        } else {
          // For new messages (timestamp IDs), save them to database
          console.log(`📝 [EDIT MODE] Saving new edited message to database`);
          await saveMessageToDatabase(currentConversation.id, editedMessage);
        }

        // Force refresh chat history to reflect changes and clear cache
        refetchChatHistory(true);

        // Also clear any message cache by adding a cache-busting parameter
        if (typeof window !== 'undefined') {
          // Clear any cached conversation data
          const cacheKeys = Object.keys(localStorage).filter(key =>
            key.startsWith('chat_') || key.startsWith('conversation_')
          );
          cacheKeys.forEach(key => localStorage.removeItem(key));
        }
      } catch (err: any) {
        console.error('Error updating conversation:', err);
        setError(`Failed to update conversation: ${err.message}`);
      }
    }

    // Now automatically send the edited message to get a response
    await sendEditedMessageToAPI(messagesToKeep);
  };

  // Send the edited conversation to get a new response
  const sendEditedMessageToAPI = async (conversationMessages: PlaygroundMessage[]) => {
    if (!selectedConfigId || conversationMessages.length === 0) return;

    setIsLoading(true);
    setError(null);

    // Start status tracking for edit mode
    messageStatus.startProcessing();

    console.log('🔄 [EDIT MODE] Sending edited conversation for new response...');

    // Prepare payload with the conversation up to the edited message
    const messagesForPayload = conversationMessages
      .filter(m => m.role === 'user' || m.role === 'assistant' || m.role === 'system')
      .map(m => {
        let contentForApi: string | PlaygroundMessageContentPart[];

        if (m.role === 'system') {
          const firstPart = m.content[0];
          if (firstPart && firstPart.type === 'text') {
            contentForApi = firstPart.text;
          } else {
            contentForApi = '';
          }
        } else if (m.content.length === 1 && m.content[0].type === 'text') {
          contentForApi = m.content[0].text;
        } else {
          contentForApi = m.content.map(part => {
            if (part.type === 'image_url') {
              return { type: 'image_url', image_url: { url: part.image_url.url } };
            }
            return { type: 'text', text: (part as PlaygroundMessageContentPartText).text };
          });
        }
        return { role: m.role, content: contentForApi };
      });

    const payload = {
      custom_api_config_id: selectedConfigId,
      messages: messagesForPayload,
      stream: useStreaming,
      // Include user ID for internal playground requests
      ...(user?.id && { _internal_user_id: user.id }),
    };



    try {
      // Update status to connecting
      messageStatus.updateStage('connecting');
      const llmStartTime = performance.now();

      const response = await fetch('/api/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.NEXT_PUBLIC_ROKEY_API_ACCESS_TOKEN || 'Y21ErgiIHrVgoSidqkShBT9npjOuP8fAlzQlTVfkmaeUUY4Kpq%*13'}`,
        },
        body: JSON.stringify(payload),
        cache: 'no-store',
      });

      const llmResponseTime = performance.now() - llmStartTime;
      console.log(`⚡ [EDIT MODE] LLM API response received in ${llmResponseTime.toFixed(1)}ms`);

      if (!response.ok) {
        const errData = await response.json();
        throw new Error(errData.error || `API Error: ${response.statusText} (Status: ${response.status})`);
      }

      // Analyze response headers to show what backend processes actually ran
      messageStatus.analyzeResponseHeaders(response.headers);

      // Brief delay to show the backend process, then switch to streaming
      setTimeout(() => {
        if (useStreaming) {
          console.log('🎯 [EDIT MODE] Response OK - switching to typing status');
          messageStatus.markStreaming();
        }
      }, 400); // Give time to show the backend process stage

      if (useStreaming && response.body) {
        // Handle streaming response with orchestration detection (same as handleSendMessage)
        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        const assistantMessageId = Date.now().toString() + '-assistant';

        const currentAssistantMessage: PlaygroundMessage = {
          id: assistantMessageId,
          role: 'assistant',
          content: [{ type: 'text', text: '' }],
        };
        setMessages(prevMessages => [...prevMessages, currentAssistantMessage]);

        let accumulatedText = '';
        let isOrchestrationDetected = false;
        let streamingStatusTimeout: NodeJS.Timeout | null = null;
        let orchestrationStatus = '';

        // Set up delayed streaming status, but allow orchestration detection to override
        streamingStatusTimeout = setTimeout(() => {
          if (!isOrchestrationDetected) {
            console.log('🎯 [EDIT MODE] Response OK - switching to typing status (no orchestration detected)');
            messageStatus.markStreaming();
          }
        }, 400);

        while (true) {
          const { done, value } = await reader.read();
          if (done) break;
          const chunk = decoder.decode(value, { stream: true });
          const lines = chunk.split('\n');

          for (const line of lines) {
            if (line.startsWith('data: ')) {
              const jsonData = line.substring(6);
              if (jsonData.trim() === '[DONE]') break;
              try {
                const parsedChunk = JSON.parse(jsonData);

                // Handle orchestration progress events
                if (parsedChunk.object === 'orchestration.progress') {
                  console.log('🎭 [ORCHESTRATION PROGRESS]', parsedChunk.data.message);
                  isOrchestrationDetected = true;
                  orchestrationStatus = parsedChunk.data.message;

                  // Cancel the delayed streaming status
                  if (streamingStatusTimeout) {
                    clearTimeout(streamingStatusTimeout);
                    streamingStatusTimeout = null;
                  }

                  // Switch to orchestration status
                  messageStatus.markOrchestrationStarted();
                  setOrchestrationStatus(orchestrationStatus);
                  return; // Don't process as content
                }

                if (parsedChunk.choices && parsedChunk.choices[0]?.delta?.content) {
                  const deltaContent = parsedChunk.choices[0].delta.content;
                  accumulatedText += deltaContent;

                  // Detect orchestration content and update status dynamically (fallback)
                  if (!isOrchestrationDetected && (
                      deltaContent.includes('🎬 **Multi-Role AI Orchestration Started!**') ||
                      deltaContent.includes('📋 **Orchestration Plan:**') ||
                      deltaContent.includes('🎭 **SYNTHESIS PHASE INITIATED**') ||
                      deltaContent.includes('🤖 **Moderator:**') ||
                      deltaContent.includes('Specialist:')
                  )) {
                    console.log('🎭 [EDIT MODE] Detected orchestration theater content - switching to orchestration status');
                    isOrchestrationDetected = true;

                    // Cancel the delayed streaming status
                    if (streamingStatusTimeout) {
                      clearTimeout(streamingStatusTimeout);
                      streamingStatusTimeout = null;
                    }

                    // Switch to orchestration status instead of marking complete
                    messageStatus.markOrchestrationStarted();
                  }

                  // Update orchestration progress based on content (fallback)
                  if (isOrchestrationDetected && !orchestrationStatus) {
                    updateOrchestrationStatus(deltaContent, messageStatus);
                  }

                  const textContent = currentAssistantMessage.content[0] as PlaygroundMessageContentPartText;
                  textContent.text = accumulatedText;
                  setMessages(prevMessages => prevMessages.map(msg =>
                    msg.id === assistantMessageId ? { ...msg, content: [textContent] } : msg
                  ));
                }
              } catch (parseError) {
                console.warn('Failed to parse stream chunk:', parseError);
              }
            }
          }
        }

        // Clean up timeout if still pending
        if (streamingStatusTimeout) {
          clearTimeout(streamingStatusTimeout);
        }

        // Save the assistant response with auto-continuation support
        if (accumulatedText && currentConversation) {
          const finalAssistantMessage: PlaygroundMessage = {
            ...currentAssistantMessage,
            content: [{ type: 'text', text: accumulatedText } as PlaygroundMessageContentPartText]
          };

          // Check if we need auto-continuation
          const needsAutoContinuation = (
            accumulatedText.includes('[SYNTHESIS CONTINUES AUTOMATICALLY...]') ||
            accumulatedText.includes('*The response will continue automatically in a new message...*')
          );

          if (needsAutoContinuation) {
            console.log('🔄 [EDIT MODE] Detected auto-continuation marker, starting new response...');

            // Save current message first
            await saveMessageToDatabase(currentConversation.id, finalAssistantMessage);

            // Start auto-continuation after a brief delay
            setTimeout(() => {
              handleAutoContinuation();
            }, 2000);
          } else {
            await saveMessageToDatabase(currentConversation.id, finalAssistantMessage);
          }
        }
      } else {
        // Handle non-streaming response
        const data = await response.json();
        let assistantContent = "Could not parse assistant's response.";

        if (data.choices?.[0]?.message?.content) {
          assistantContent = data.choices[0].message.content;
        } else if (data.content?.[0]?.text) {
          assistantContent = data.content[0].text;
        } else if (typeof data.text === 'string') {
          assistantContent = data.text;
        }

        const assistantMessage: PlaygroundMessage = {
          id: Date.now().toString() + '-assistant',
          role: 'assistant',
          content: [{ type: 'text', text: assistantContent }],
        };
        setMessages(prevMessages => [...prevMessages, assistantMessage]);

        // Save the assistant response
        if (currentConversation) {
          await saveMessageToDatabase(currentConversation.id, assistantMessage);
        }
      }

    } catch (err: any) {
      console.error("Edit mode API call error:", err);
      const errorMessage: PlaygroundMessage = {
        id: Date.now().toString() + '-error',
        role: 'error',
        content: [{ type: 'text', text: err.message || 'An unexpected error occurred.' }],
      };
      setMessages(prevMessages => [...prevMessages, errorMessage]);
      setError(err.message);
    } finally {
      setIsLoading(false);

      // Mark status as complete and log performance
      messageStatus.markComplete();
      console.log('🎯 [EDIT MODE] Processing complete');
    }
  };

  // Handle retry message with optional specific API key
  const handleRetryMessage = async (messageIndex: number, apiKeyId?: string) => {
    if (!selectedConfigId || messageIndex < 0 || messageIndex >= messages.length) return;

    const messageToRetry = messages[messageIndex];
    if (messageToRetry.role !== 'assistant') return;

    setIsLoading(true);
    setError(null);

    // Reset orchestration status
    setOrchestrationStatus('');

    // Start status tracking for retry
    messageStatus.startProcessing();

    console.log('🔄 [RETRY] Retrying message with', apiKeyId ? `specific key: ${apiKeyId}` : 'same model');

    // Remove the assistant message and any messages after it
    const messagesToKeep = messages.slice(0, messageIndex);
    setMessages(messagesToKeep);

    // If we have a current conversation, delete the retried message and subsequent ones from database
    if (currentConversation) {
      try {
        const messagesToDelete = messages.slice(messageIndex);
        console.log(`🗑️ [RETRY] Deleting ${messagesToDelete.length} messages from retry point`);

        // Delete all messages from the retry point onwards using timestamp-based deletion
        if (messagesToDelete.length > 0) {
          const retryMessage = messages[messageIndex];
          const retryMessageTimestamp = parseInt(retryMessage.id) || Date.now();

          console.log(`🗑️ [RETRY] Deleting all messages from timestamp: ${retryMessageTimestamp}`);

          const deleteResponse = await fetch(`/api/chat/messages/delete-after-timestamp`, {
            method: 'DELETE',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              conversation_id: currentConversation.id,
              from_timestamp: retryMessageTimestamp // Use 'from' instead of 'after' to include the retry message
            }),
          });

          if (!deleteResponse.ok) {
            console.error(`Failed to delete messages from timestamp:`, await deleteResponse.text());
          } else {
            const result = await deleteResponse.json();
            console.log(`✅ [RETRY] Successfully deleted ${result.deleted_count} messages`);
          }
        }

        // Refresh chat history to reflect changes
        refetchChatHistory(true);
      } catch (err: any) {
        console.error('Error deleting retried messages:', err);
      }
    }

    // Prepare payload with messages up to the retry point
    const messagesForPayload = messagesToKeep
      .filter(m => m.role === 'user' || m.role === 'assistant' || m.role === 'system')
      .map(m => {
        let contentForApi: string | PlaygroundMessageContentPart[];

        if (m.role === 'system') {
          const firstPart = m.content[0];
          if (firstPart && firstPart.type === 'text') {
            contentForApi = firstPart.text;
          } else {
            contentForApi = '';
          }
        } else if (m.content.length === 1 && m.content[0].type === 'text') {
          contentForApi = m.content[0].text;
        } else {
          contentForApi = m.content.map(part => {
            if (part.type === 'image_url') {
              return { type: 'image_url', image_url: { url: part.image_url.url } };
            }
            return { type: 'text', text: (part as PlaygroundMessageContentPartText).text };
          });
        }
        return { role: m.role, content: contentForApi };
      });

    const payload = {
      custom_api_config_id: selectedConfigId,
      messages: messagesForPayload,
      stream: useStreaming,
      ...(apiKeyId && { specific_api_key_id: apiKeyId }), // Add specific key if provided
      // Include user ID for internal playground requests
      ...(user?.id && { _internal_user_id: user.id }),
    };

    try {
      console.log('🚀 [RETRY] Starting retry API call...');
      // Update status to connecting
      messageStatus.updateStage('connecting');
      const llmStartTime = performance.now();

      const response = await fetch('/api/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.NEXT_PUBLIC_ROKEY_API_ACCESS_TOKEN || 'Y21ErgiIHrVgoSidqkShBT9npjOuP8fAlzQlTVfkmaeUUY4Kpq%*13'}`,
        },
        body: JSON.stringify(payload),
        cache: 'no-store',
      });

      const llmResponseTime = performance.now() - llmStartTime;
      console.log(`⚡ [RETRY] LLM API response received in ${llmResponseTime.toFixed(1)}ms`);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
      }

      // Analyze response headers to show what backend processes actually ran
      messageStatus.analyzeResponseHeaders(response.headers);

      // Check for orchestration headers in retry
      const orchestrationId = response.headers.get('X-RoKey-Orchestration-ID');
      const orchestrationActive = response.headers.get('X-RoKey-Orchestration-Active');

      if (orchestrationId && orchestrationActive === 'true') {
        console.log('🎭 [ORCHESTRATION] Detected orchestration headers in retry - showing canvas');
        setOrchestrationExecutionId(orchestrationId);
        setShowOrchestration(true);
        setOrchestrationComplete(false); // Reset completion state
      }

      // Brief delay to show the backend process, then switch to streaming
      setTimeout(() => {
        if (useStreaming) {
          console.log('🎯 [RETRY] Response OK - switching to typing status');
          messageStatus.markStreaming();
        }
      }, 400); // Give time to show the backend process stage

      // Handle streaming or non-streaming response (reuse existing logic)
      if (useStreaming && response.body) {
        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let accumulatedText = '';

        const currentAssistantMessage: PlaygroundMessage = {
          id: Date.now().toString() + '-assistant-retry',
          role: 'assistant',
          content: [{ type: 'text', text: '' }],
        };

        setMessages(prevMessages => [...prevMessages, currentAssistantMessage]);

        try {
          while (true) {
            const { done, value } = await reader.read();
            if (done) break;

            const chunk = decoder.decode(value, { stream: true });
            const lines = chunk.split('\n');

            for (const line of lines) {
              if (line.startsWith('data: ')) {
                const data = line.slice(6);
                if (data === '[DONE]') continue;

                try {
                  const parsed = JSON.parse(data);
                  if (parsed.choices?.[0]?.delta?.content) {
                    const newContent = parsed.choices[0].delta.content;
                    accumulatedText += newContent;

                    // Detect orchestration content and update status dynamically
                    if (newContent.includes('🎬 **Multi-Role AI Orchestration Started!**') ||
                        newContent.includes('📋 **Orchestration Plan:**') ||
                        newContent.includes('🎭 **SYNTHESIS PHASE INITIATED**') ||
                        newContent.includes('🤖 **Moderator:**') ||
                        newContent.includes('Specialist:')) {
                      console.log('🎭 [ORCHESTRATION] Detected orchestration theater content - switching to orchestration status');
                      messageStatus.markOrchestrationStarted();
                      updateOrchestrationStatus(newContent, messageStatus);
                    } else if (orchestrationStatus) {
                      // Continue updating orchestration status if already in orchestration mode
                      updateOrchestrationStatus(newContent, messageStatus);
                    }

                    setMessages(prevMessages =>
                      prevMessages.map(msg =>
                        msg.id === currentAssistantMessage.id
                          ? {
                              ...msg,
                              content: [{ type: 'text', text: accumulatedText }]
                            }
                          : msg
                      )
                    );
                  }
                } catch (parseError) {
                  console.warn('Failed to parse streaming chunk:', parseError);
                }
              }
            }
          }
        } finally {
          reader.releaseLock();
        }

        // Save final assistant message
        if (accumulatedText && currentConversation) {
          const finalAssistantMessage: PlaygroundMessage = {
            ...currentAssistantMessage,
            content: [{ type: 'text', text: accumulatedText }]
          };
          await saveMessageToDatabase(currentConversation.id, finalAssistantMessage);
        }
      } else {
        // Non-streaming response
        const data = await response.json();
        let assistantContent = '';

        if (data.choices && data.choices.length > 0 && data.choices[0].message) {
          assistantContent = data.choices[0].message.content;
        } else if (data.content && Array.isArray(data.content) && data.content.length > 0) {
          assistantContent = data.content[0].text;
        }

        const assistantMessage: PlaygroundMessage = {
          id: Date.now().toString() + '-assistant-retry',
          role: 'assistant',
          content: [{ type: 'text', text: assistantContent }],
        };
        setMessages(prevMessages => [...prevMessages, assistantMessage]);

        // Save assistant message
        if (currentConversation) {
          await saveMessageToDatabase(currentConversation.id, assistantMessage);
        }
      }

    } catch (err: any) {
      console.error("Retry API call error:", err);
      const errorMessage: PlaygroundMessage = {
        id: Date.now().toString() + '-error-retry',
        role: 'error',
        content: [{ type: 'text', text: err.message || 'An unexpected error occurred during retry.' }],
      };
      setMessages(prevMessages => [...prevMessages, errorMessage]);
      setError(err.message);

      // Save error message
      if (currentConversation) {
        await saveMessageToDatabase(currentConversation.id, errorMessage);
      }
    } finally {
      setIsLoading(false);

      // Mark status as complete and log performance
      messageStatus.markComplete();
      console.log('🎯 [RETRY] Processing complete');
    }
  };

  const handleSendMessage = useCallback(async (e?: FormEvent<HTMLFormElement>) => {
    if (e) e.preventDefault();
    // Allow sending if there's text OR images
    if ((!messageInput.trim() && imageFiles.length === 0) || !selectedConfigId) return;

    // Check if this is a continuation request
    const inputText = messageInput.trim().toLowerCase();
    if (inputText === 'continue' && messages.length > 0) {
      console.log('🔄 [CONTINUE] Detected manual continuation request, routing to auto-continuation...');

      // Clear the input
      setMessageInput('');

      // Route to auto-continuation instead of normal message flow
      await handleAutoContinuation();
      return;
    }

    setIsLoading(true);
    setError(null);

    // Reset orchestration status
    setOrchestrationStatus('');

    // Start enhanced status tracking
    messageStatus.startProcessing();

    // Phase 1 Optimization: Performance tracking
    const messagingStartTime = performance.now();
    console.log('🚀 [MESSAGING FLOW] Starting optimized parallel processing...');

    // Capture current input and images before clearing them
    const currentMessageInput = messageInput.trim();
    const currentImageFiles = [...imageFiles];
    const currentImagePreviews = [...imagePreviews];

    // Clear input and images immediately to prevent them from showing after send
    setMessageInput('');
    handleRemoveImage();

    const userMessageContentParts: PlaygroundMessageContentPart[] = [];
    let apiMessageContentParts: any[] = []; // For the API payload, image_url.url will be base64

    if (currentMessageInput) {
      userMessageContentParts.push({ type: 'text', text: currentMessageInput });
      apiMessageContentParts.push({ type: 'text', text: currentMessageInput });
    }

    // Process all images
    if (currentImageFiles.length > 0) {
      try {
        for (let i = 0; i < currentImageFiles.length; i++) {
          const file = currentImageFiles[i];
          const preview = currentImagePreviews[i];
          const base64ImageData = await fileToBase64(file);

          // For UI display (uses the preview which is already base64)
          userMessageContentParts.push({ type: 'image_url', image_url: { url: preview } });
          // For API payload
          apiMessageContentParts.push({ type: 'image_url', image_url: { url: base64ImageData } });
        }
      } catch (imgErr) {
        console.error("Error converting images to base64:", imgErr);
        setError("Failed to process one or more images. Please try again.");
        setIsLoading(false);
        // Restore the input and images if there was an error
        setMessageInput(currentMessageInput);
        setImageFiles(currentImageFiles);
        setImagePreviews(currentImagePreviews);
        return;
      }
    }

    const newUserMessage: PlaygroundMessage = {
      id: Date.now().toString(),
      role: 'user',
      content: userMessageContentParts,
    };
    setMessages(prevMessages => [...prevMessages, newUserMessage]);

    // Phase 1 Optimization: Start conversation creation and user message saving in background
    // Don't wait for these operations - they can happen in parallel with LLM call
    const conversationId = currentConversation?.id;
    let conversationPromise: Promise<string | null> = Promise.resolve(conversationId || null);
    let userMessageSavePromise: Promise<void> = Promise.resolve();

    if (!conversationId && !currentConversation) {
      console.log('🔄 [PARALLEL] Starting conversation creation in background...');
      conversationPromise = createNewConversation(newUserMessage);
    }

    // Start user message saving in background (will wait for conversation if needed)
    userMessageSavePromise = conversationPromise.then(async (convId) => {
      if (convId) {
        console.log('💾 [PARALLEL] Saving user message in background...');
        await saveMessageToDatabase(convId, newUserMessage);
        console.log('✅ [PARALLEL] User message saved');
      }
    }).catch(err => {
      console.error('❌ [PARALLEL] User message save failed:', err);
    });

    // Prepare payload.messages by transforming existing messages and adding the new one
    const existingMessagesForPayload = messages
      .filter(m => m.role === 'user' || m.role === 'assistant' || m.role === 'system')
      .map(m => {
        let contentForApi: string | PlaygroundMessageContentPart[];

        if (m.role === 'system') {
          // System messages are always simple text strings
          // Their content in PlaygroundMessage is [{type: 'text', text: 'Actual system prompt'}]
          const firstPart = m.content[0];
          if (firstPart && firstPart.type === 'text') {
            contentForApi = firstPart.text;
          } else {
            contentForApi = ''; // Fallback, though system messages should always be text
          }
        } else if (m.content.length === 1 && m.content[0].type === 'text') {
          // Single text part for user/assistant, send as string for API
          contentForApi = m.content[0].text;
        } else {
          // Multimodal content (e.g., user message with image) or multiple parts
          contentForApi = m.content.map(part => {
            if (part.type === 'image_url') {
              // The part.image_url.url from messages state is the base64 data URL (preview)
              // This is what we want to send to the backend.
              return { type: 'image_url', image_url: { url: part.image_url.url } };
            }
            // Ensure it's properly cast for text part before accessing .text
            return { type: 'text', text: (part as PlaygroundMessageContentPartText).text };
          });
        }
        return { role: m.role, content: contentForApi };
      });

    // Check if this is a workflow execution
    const isWorkflow = selectedConfigId.startsWith('workflow_');
    let apiEndpoint = '/api/v1/chat/completions';
    let payload: any;
    let headers: any = {
      'Content-Type': 'application/json',
    };

    if (isWorkflow) {
      // Workflow execution
      const workflowId = selectedConfigId.replace('workflow_', '');
      const workflow = workflows.find(w => w.id === workflowId);

      if (!workflow) {
        throw new Error('Selected workflow not found');
      }

      // Connect to workflow stream for real-time updates
      if (useStreaming) {
        const eventSource = new EventSource(`/api/workflow/stream/${workflowId}`);
        setWorkflowStream(eventSource);

        // Wait for WebSocket connection to be established before proceeding
        const connectionPromise = new Promise<void>((resolve, reject) => {
          const timeout = setTimeout(() => {
            reject(new Error('WebSocket connection timeout'));
          }, 5000); // 5 second timeout

          eventSource.onmessage = (event) => {
            try {
              const data = JSON.parse(event.data);
              console.log('🔄 [Workflow Stream]', data);

              // Check if this is the connection established event
              if (data.type === 'connection_established') {
                setWorkflowStatus(`🔗 ${data.data.message}`);
                clearTimeout(timeout);
                resolve(); // Connection is ready, proceed with execution
              }

              // Update workflow status for other events
              if (data.type === 'node_start') {
                setWorkflowStatus(`🔄 Executing: ${data.data.nodeName || data.data.nodeType}`);
              } else if (data.type === 'node_complete') {
                setWorkflowStatus(`✅ Completed: ${data.data.nodeName || data.data.nodeType}`);
              } else if (data.type === 'workflow_complete') {
                setWorkflowStatus('🎉 Workflow completed successfully');
                setTimeout(() => setWorkflowStatus(''), 3000);
              } else if (data.type === 'error') {
                setWorkflowStatus(`❌ Error: ${data.data.message}`);
              }
            } catch (error) {
              console.error('Failed to parse workflow stream data:', error);
            }
          };

          eventSource.onerror = (error) => {
            console.error('Workflow stream error:', error);
            setWorkflowStatus('❌ Connection to workflow stream lost');
            clearTimeout(timeout);
            reject(error);
          };
        });

        // Wait for connection to be established before proceeding
        try {
          await connectionPromise;
          console.log('✅ [Workflow Stream] Connection established, proceeding with execution');
        } catch (error) {
          console.error('❌ [Workflow Stream] Connection failed:', error);
          setWorkflowStatus('❌ Failed to connect to workflow stream');
          eventSource.close();
          setWorkflowStream(null);
          throw error;
        }
      }

      // Get workflow data for internal execution
      const workflowResponse = await fetch(`/api/workflows?id=${workflowId}`);
      if (!workflowResponse.ok) {
        throw new Error('Failed to get workflow data');
      }
      const workflowData = await workflowResponse.json();

      if (!workflowData.workflow) {
        throw new Error('Workflow not found');
      }

      // Use internal workflow execution endpoint for playground
      apiEndpoint = '/api/manual-build/execute-workflow';

      // For playground execution, we'll use internal authentication
      // and pass the workflow ID and user input directly

      payload = {
        workflowId: workflowId,
        userInput: apiMessageContentParts.length === 1 && apiMessageContentParts[0].type === 'text'
          ? apiMessageContentParts[0].text
          : JSON.stringify(apiMessageContentParts),
        nodes: workflowData.workflow.nodes,
        edges: workflowData.workflow.edges,
        // Pass full conversation history for context (like regular router configs)
        messages: messages.map(m => ({
          role: m.role,
          content: m.content.length === 1 && m.content[0].type === 'text'
            ? m.content[0].text
            : m.content
        })),
        // Enable streaming for workflow execution
        stream: useStreaming
      };
    } else {
      // Regular router execution
      headers['Authorization'] = `Bearer ${process.env.NEXT_PUBLIC_ROKEY_API_ACCESS_TOKEN || 'Y21ErgiIHrVgoSidqkShBT9npjOuP8fAlzQlTVfkmaeUUY4Kpq%*13'}`;

      payload = {
        custom_api_config_id: selectedConfigId,
        messages: [
          ...existingMessagesForPayload,
          { role: 'user', content: apiMessageContentParts.length === 1 && apiMessageContentParts[0].type === 'text' ? apiMessageContentParts[0].text : apiMessageContentParts }
        ],
        stream: useStreaming,
        // Include user ID for internal playground requests
        ...(user?.id && { _internal_user_id: user.id }),
      };
    }

    try {
      // Phase 1 Optimization: Start LLM call immediately in parallel with background operations
      if (process.env.NODE_ENV === 'development') {
        console.log(`🚀 [PARALLEL] Starting ${isWorkflow ? 'Workflow' : 'LLM'} API call...`);
      }
      messageStatus.updateStage('connecting');
      const llmStartTime = performance.now();

      const response = await fetch(apiEndpoint, {
        method: 'POST',
        headers,
        body: JSON.stringify(payload),
        // Conservative performance optimizations
        cache: 'no-store',
      });

      const llmResponseTime = performance.now() - llmStartTime;
      if (process.env.NODE_ENV === 'development') {
        console.log(`⚡ [PARALLEL] ${isWorkflow ? 'Workflow' : 'LLM'} API response received in ${llmResponseTime.toFixed(1)}ms`);
      }

      // Additional workflow-specific logging
      if (isWorkflow && process.env.NODE_ENV === 'development') {
        const workflowId = selectedConfigId.replace('workflow_', '');
        const workflow = workflows.find(w => w.id === workflowId);
        console.log(`🔧 [WORKFLOW] Executed: ${workflow?.name || 'Unknown'} (${workflow?.node_count || 0} nodes)`);
        console.log(`🔧 [WORKFLOW] Response status: ${response.status}`);
        console.log(`🔧 [WORKFLOW] Response headers:`, Object.fromEntries(response.headers.entries()));
      }

      if (!response.ok) {
        const errData = await response.json();
        if (isWorkflow && process.env.NODE_ENV === 'development') {
          console.error(`❌ [WORKFLOW] Execution failed:`, errData);
        }
        throw new Error(errData.error || `API Error: ${response.statusText} (Status: ${response.status})`);
      }

      // Analyze response headers to show what backend processes actually ran
      messageStatus.analyzeResponseHeaders(response.headers);

      // Check for orchestration headers
      const orchestrationId = response.headers.get('X-RoKey-Orchestration-ID');
      const orchestrationActive = response.headers.get('X-RoKey-Orchestration-Active');

      console.log('🎭 [DEBUG] Checking orchestration headers:', {
        orchestrationId,
        orchestrationActive,
        allHeaders: Object.fromEntries(response.headers.entries())
      });

      if (orchestrationId && orchestrationActive === 'true') {
        if (process.env.NODE_ENV === 'development') {
          console.log('🎭 [ORCHESTRATION] Detected orchestration headers - showing canvas');
        }
        setOrchestrationExecutionId(orchestrationId);
        setShowOrchestration(true);
        setOrchestrationComplete(false); // Reset completion state
      } else if (process.env.NODE_ENV === 'development') {
        console.log('🎭 [DEBUG] No orchestration headers found or not active');
      }

      // Handle workflow execution response
      if (isWorkflow) {
        if (useStreaming && response.body) {
          // Handle streaming workflow response
          console.log('🌊 [WORKFLOW] Processing streaming response...');

          const reader = response.body.getReader();
          const decoder = new TextDecoder();
          let accumulatedText = '';

          const currentAssistantMessage: PlaygroundMessage = {
            id: Date.now().toString() + '-assistant',
            role: 'assistant',
            content: [{ type: 'text', text: '' }],
          };

          setMessages(prevMessages => [...prevMessages, currentAssistantMessage]);

          // Brief delay to show the backend process, then switch to streaming
          setTimeout(() => {
            console.log('🎯 [WORKFLOW] Response OK - switching to typing status');
            messageStatus.markStreaming();
          }, 400);

          try {
            while (true) {
              const { done, value } = await reader.read();
              if (done) break;

              const chunk = decoder.decode(value, { stream: true });
              const lines = chunk.split('\n');

              for (const line of lines) {
                if (line.startsWith('data: ')) {
                  const data = line.slice(6);
                  if (data === '[DONE]') continue;

                  try {
                    const parsed = JSON.parse(data);
                    const content = parsed.choices?.[0]?.delta?.content || '';

                    if (content) {
                      accumulatedText += content;

                      // Update the message in real-time
                      setMessages(prevMessages =>
                        prevMessages.map(msg =>
                          msg.id === currentAssistantMessage.id
                            ? { ...msg, content: [{ type: 'text', text: accumulatedText }] }
                            : msg
                        )
                      );

                      // Simulate typing effect with artificial chunking
                      await new Promise(resolve => setTimeout(resolve, 15));
                    }
                  } catch (parseError) {
                    console.warn('[WORKFLOW] Error parsing streaming data:', parseError);
                  }
                }
              }
            }

            console.log('🎉 [WORKFLOW] Streaming completed:', accumulatedText.length, 'characters');

            // Save final message to database
            const finalConversationId = await conversationPromise;
            if (finalConversationId) {
              const finalMessage = {
                ...currentAssistantMessage,
                content: [{ type: 'text', text: accumulatedText }]
              };
              await saveMessageToDatabase(finalConversationId, finalMessage);
            }

          } catch (streamError) {
            console.error('[WORKFLOW] Streaming error:', streamError);
            messageStatus.markError();
          }
        } else {
          // Handle non-streaming workflow response (backward compatibility)
          const workflowResult = await response.json();
          console.log('🎉 [WORKFLOW] Execution completed:', workflowResult);

          // Extract the actual AI response from the workflow result
          let responseText = '';

          // Try to extract finalOutput from the workflow result
          if (workflowResult.result?.finalOutput) {
            responseText = workflowResult.result.finalOutput;
          } else if (workflowResult.result?.response) {
            responseText = workflowResult.result.response;
          } else if (typeof workflowResult.result === 'string') {
            responseText = workflowResult.result;
          } else {
            // Fallback to JSON display for debugging
            responseText = JSON.stringify(workflowResult.result, null, 2);
          }

          // Create assistant message with the extracted response
          const assistantMessageId = Date.now().toString() + '-assistant';
          const assistantMessage: PlaygroundMessage = {
            id: assistantMessageId,
            role: 'assistant',
            content: [{
              type: 'text',
              text: responseText
            }],
          };

          setMessages(prevMessages => [...prevMessages, assistantMessage]);

          // Save assistant message to database
          const finalConversationId = await conversationPromise;
          if (finalConversationId) {
            await saveMessageToDatabase(finalConversationId, assistantMessage);
          }
        }

        // Close workflow stream if it exists
        if (workflowStream) {
          workflowStream.close();
          setWorkflowStream(null);
        }

        setWorkflowStatus('🎉 Workflow completed successfully');
        setTimeout(() => setWorkflowStatus(''), 3000);

        // Mark message processing as complete
        messageStatus.markComplete();

      } else if (useStreaming && response.body) {
        // Handle regular streaming responses
        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let assistantMessageId = Date.now().toString() + '-assistant';
        let currentAssistantMessage: PlaygroundMessage = {
          id: assistantMessageId,
          role: 'assistant',
          content: [{ type: 'text', text: '' }],
        };
        setMessages(prevMessages => [...prevMessages, currentAssistantMessage]);

        let accumulatedText = '';
        let isOrchestrationDetected = false;
        let streamingStatusTimeout: NodeJS.Timeout | null = null;
        let orchestrationStatus = '';

        // Set up delayed streaming status, but allow orchestration detection to override
        streamingStatusTimeout = setTimeout(() => {
          if (!isOrchestrationDetected) {
            console.log('🎯 Response OK - switching to typing status (no orchestration detected)');
            messageStatus.markStreaming();
          }
        }, 400);

        while (true) {
          const { done, value } = await reader.read();
          if (done) break;
          const chunk = decoder.decode(value, { stream: true });
          const lines = chunk.split('\n');
          for (const line of lines) {
            if (line.startsWith('data: ')) {
              const jsonData = line.substring(6);
              if (jsonData.trim() === '[DONE]') break;

              console.log('📦 [STREAM DEBUG] Received chunk:', jsonData);

              try {
                const parsedChunk = JSON.parse(jsonData);
                console.log('📦 [STREAM DEBUG] Parsed chunk:', parsedChunk);

                // Handle orchestration progress events
                if (parsedChunk.object === 'orchestration.progress') {
                  console.log('🎭 [ORCHESTRATION PROGRESS]', parsedChunk.data.message);
                  isOrchestrationDetected = true;
                  orchestrationStatus = parsedChunk.data.message;

                  // Cancel the delayed streaming status
                  if (streamingStatusTimeout) {
                    clearTimeout(streamingStatusTimeout);
                    streamingStatusTimeout = null;
                  }

                  // Switch to orchestration status
                  messageStatus.markOrchestrationStarted();
                  setOrchestrationStatus(orchestrationStatus);
                  continue; // Don't process as content, but continue reading stream
                }

                // Handle workflow response format
                if (parsedChunk.result && isWorkflow) {
                  if (process.env.NODE_ENV === 'development') {
                    console.log('🔧 [WORKFLOW] Processing workflow result:', parsedChunk);
                  }
                  const workflowResult = typeof parsedChunk.result === 'string' ? parsedChunk.result : JSON.stringify(parsedChunk.result, null, 2);
                  accumulatedText += workflowResult;
                } else if (parsedChunk.choices && parsedChunk.choices[0]?.delta?.content) {
                  const deltaContent = parsedChunk.choices[0].delta.content;
                  accumulatedText += deltaContent;

                  // Detect orchestration content and update status dynamically (fallback)
                  if (!isOrchestrationDetected && (
                      deltaContent.includes('🎬 **Multi-Role AI Orchestration Started!**') ||
                      deltaContent.includes('📋 **Orchestration Plan:**') ||
                      deltaContent.includes('🎭 **SYNTHESIS PHASE INITIATED**') ||
                      deltaContent.includes('🤖 **Moderator:**') ||
                      deltaContent.includes('Specialist:')
                  )) {
                    console.log('🎭 [ORCHESTRATION] Detected orchestration theater content - switching to orchestration status');
                    isOrchestrationDetected = true;

                    // Cancel the delayed streaming status
                    if (streamingStatusTimeout) {
                      clearTimeout(streamingStatusTimeout);
                      streamingStatusTimeout = null;
                    }

                    // Switch to orchestration status instead of marking complete
                    messageStatus.markOrchestrationStarted();
                  }

                  // Update orchestration progress based on content (fallback)
                  if (isOrchestrationDetected && !orchestrationStatus) {
                    updateOrchestrationStatus(deltaContent, messageStatus);
                  }

                  const textContent = currentAssistantMessage.content[0] as PlaygroundMessageContentPartText;
                  textContent.text = accumulatedText;
                  setMessages(prevMessages => prevMessages.map(msg =>
                    msg.id === assistantMessageId ? { ...msg, content: [textContent] } : msg
                  ));
                }
              } catch (parseError) {
                console.warn('Playground: Failed to parse stream chunk JSON:', jsonData, parseError);
              }
            }
          }
        }

        // Clean up timeout if still pending
        if (streamingStatusTimeout) {
          clearTimeout(streamingStatusTimeout);
        }

        if (accumulatedText) {
          const finalAssistantMessage: PlaygroundMessage = {
            ...currentAssistantMessage,
            content: [{ type: 'text', text: accumulatedText } as PlaygroundMessageContentPartText]
          };

          // Check response headers to determine if this is chunked synthesis
          const synthesisProgress = response.headers.get('X-Synthesis-Progress');
          const synthesisComplete = response.headers.get('X-Synthesis-Complete');
          const isChunkedSynthesis = synthesisProgress !== null;

          // Check if we need auto-continuation
          const needsAutoContinuation = (
            accumulatedText.includes('[SYNTHESIS CONTINUES AUTOMATICALLY...]') ||
            accumulatedText.includes('*The response will continue automatically in a new message...*')
          );

          if (needsAutoContinuation) {
            console.log('🔄 [AUTO-CONTINUE] Detected auto-continuation marker, starting new response...');

            // Save current message first
            conversationPromise.then(async (convId) => {
              if (convId) await saveMessageToDatabase(convId, finalAssistantMessage);
            });

            // For chunked synthesis, start continuation immediately
            // For regular synthesis, add a delay
            const delay = isChunkedSynthesis ? 1000 : 2000;
            setTimeout(() => {
              handleAutoContinuation();
            }, delay);
          } else {
            conversationPromise.then(async (convId) => {
              if (convId) await saveMessageToDatabase(convId, finalAssistantMessage);
            });
          }
        }
      }

    } catch (err: any) {
      console.error("Playground API call error:", err);
      const errorMessage: PlaygroundMessage = {
        id: Date.now().toString() + '-error',
        role: 'error',
        content: [{ type: 'text', text: err.message || 'An unexpected error occurred.' }],
      };
      setMessages(prevMessages => [...prevMessages, errorMessage]);
      setError(err.message);

      // Phase 1 Optimization: Save error message in background
      conversationPromise.then(async (convId) => {
        if (convId) {
          console.log('💾 [PARALLEL] Saving error message in background...');
          await saveMessageToDatabase(convId, errorMessage);
          console.log('✅ [PARALLEL] Error message saved');
        }
      }).catch(saveErr => {
        console.error('❌ [PARALLEL] Error message save failed:', saveErr);
      });
    } finally {
      setIsLoading(false);

      // Cleanup workflow stream if it exists
      if (workflowStream) {
        workflowStream.close();
        setWorkflowStream(null);
        // Keep workflow status for a bit longer to show completion
        setTimeout(() => setWorkflowStatus(''), 3000);
      }

      // Mark status as complete and log performance
      messageStatus.markComplete();
      logStatusPerformance(messageStatus.stageHistory);

      // Phase 1 Optimization: Performance summary
      const totalMessagingTime = performance.now() - messagingStartTime;
      console.log(`📊 [MESSAGING FLOW] Total time: ${totalMessagingTime.toFixed(1)}ms`);

      // Phase 1 Optimization: Refresh chat history in background, don't block UI
      conversationPromise.then(async (convId) => {
        if (convId && !currentConversation) {
          console.log('🔄 [PARALLEL] Refreshing chat history in background...');
          refetchChatHistory(true);
          console.log('✅ [PARALLEL] Chat history refreshed');
        }
      }).catch(refreshErr => {
        console.error('❌ [PARALLEL] Chat history refresh failed:', refreshErr);
      });
    }
  }, [messageInput, imageFiles, selectedConfigId, isLoading, currentConversation, messages, messageStatus, useStreaming, orchestrationStatus, setMessages, setError, setIsLoading, setCurrentConversation, setMessageInput, setImageFiles, setImagePreviews, refetchChatHistory]);

  return (
    <div className="min-h-screen bg-[#040716] flex">
      {/* Main Content Area */}
      <div className={`flex-1 flex flex-col transition-all duration-300 ease-in-out`} style={{
        marginLeft: sidebarWidth,
        marginRight: isCanvasOpen && !isCanvasMinimized
          ? '50%' // Canvas takes 50% of screen width
          : isHistoryCollapsed ? '0px' : '320px'
      }}>
        {/* Fixed Header */}
        <div className="fixed top-0 z-40 bg-[#040716]/95 backdrop-blur-sm border-b border-gray-800/50 transition-all duration-300 ease-in-out" style={{
          left: sidebarWidth,
          right: isCanvasOpen && !isCanvasMinimized
            ? '50%' // Canvas takes 50% of screen width
            : isHistoryCollapsed ? '0px' : '320px'
        }}>
          <div className="px-6 py-2">
            <div className="flex items-center justify-between">
              {/* Left: Enhanced Model Selector */}
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  {selectedConfigId ? (
                    <>
                      <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                      <span className="text-sm font-medium text-white">Connected</span>
                    </>
                  ) : (
                    <>
                      <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
                      <span className="text-sm font-medium text-gray-400">Not Connected</span>
                    </>
                  )}
                </div>
                <div className="relative">
                  <select
                    value={selectedConfigId}
                    onChange={(e) => handleConfigChange(e.target.value)}
                    disabled={customConfigs.length === 0 && workflows.length === 0}
                    className="appearance-none px-4 py-2.5 pr-10 bg-gray-800/50 border border-gray-700/50 rounded-xl text-sm font-medium text-white focus:outline-none focus:ring-2 focus:ring-blue-500/20 focus:border-gray-600 transition-all duration-200 shadow-sm hover:shadow-md min-w-[200px]"
                  >
                    <option value="">Select Router or Workflow</option>

                    {/* Router Configurations */}
                    {customConfigs.length > 0 && (
                      <optgroup label="🔀 Router Configurations">
                        {customConfigs.map((config) => (
                          <option key={config.id} value={config.id}>
                            {config.name}
                          </option>
                        ))}
                      </optgroup>
                    )}

                    {/* Manual Build Workflows */}
                    {workflows.length > 0 && (
                      <optgroup label="⚡ Manual Build Workflows">
                        {workflows.filter(w => w.is_active).map((workflow) => (
                          <option key={`workflow_${workflow.id}`} value={`workflow_${workflow.id}`}>
                            {workflow.name}
                          </option>
                        ))}
                      </optgroup>
                    )}
                  </select>
                  <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                    <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </div>
                </div>
              </div>

              {/* Right: Enhanced Streaming Toggle */}
              <div className="flex items-center space-x-3">
                <span className="text-sm font-medium text-white">Streaming</span>
                <button
                  onClick={() => setUseStreaming(!useStreaming)}
                  className={`relative inline-flex h-7 w-12 items-center rounded-full transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500/20 shadow-sm ${
                    useStreaming ? 'bg-blue-500 shadow-blue-200' : 'bg-gray-600'
                  }`}
                >
                  <span
                    className={`inline-block h-5 w-5 transform rounded-full bg-white transition-transform duration-200 shadow-sm ${
                      useStreaming ? 'translate-x-6' : 'translate-x-1'
                    }`}
                  />
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Messages Container */}
        <div className="flex-1 flex flex-col pt-0 pb-32">
          {messages.length === 0 && !currentConversation ? (
            /* Welcome Screen - Static, centered, no scroll */
            <div className="fixed inset-0 flex items-center justify-center px-6" style={{
              top: '80px', // Account for header height
              left: sidebarWidth,
              right: isCanvasOpen && !isCanvasMinimized ? '50%' : (isHistoryCollapsed ? '0px' : '320px'),
              bottom: '120px' // Account for input area height
            }}>
              <div className={`w-full mx-auto transition-all duration-300 ${
                isCanvasOpen && !isCanvasMinimized ? 'max-w-2xl' : 'max-w-3xl'
              }`}>
                <div className="flex flex-col items-center justify-center">
                  <div className="text-center mb-12">
                    <h1 className="text-4xl font-bold mb-4">
                      <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400">
                        Hello{firstName ? ` ${firstName}` : ''}
                      </span>
                    </h1>
                    <p className="text-lg text-gray-400 max-w-md mx-auto">
                      Select a router configuration or Manual Build workflow and explore RouKey's intelligent routing capabilities below.
                    </p>
                  </div>

                  {/* Conversation Starters Grid - Gemini Style */}
                  <div className="grid grid-cols-2 gap-4 w-full max-w-2xl">
                    {conversationStarters.map((starter) => (
                      <button
                        key={starter.id}
                        onClick={() => handleStarterClick(starter.prompt)}
                        disabled={!selectedConfigId}
                        className={`group relative p-6 bg-gray-800/30 rounded-2xl border border-gray-700/50 hover:border-gray-600/70 hover:bg-gray-800/50 transition-all duration-200 text-left disabled:opacity-50 disabled:cursor-not-allowed ${
                          !selectedConfigId ? 'cursor-not-allowed' : 'cursor-pointer hover:scale-[1.02]'
                        }`}
                      >
                        <div className="flex items-start space-x-4">
                          <div className={`w-12 h-12 rounded-xl flex items-center justify-center text-xl bg-white/10 text-white group-hover:scale-110 transition-transform duration-200`}>
                            {starter.icon}
                          </div>
                          <div className="flex-1 min-w-0">
                            <h3 className="font-semibold text-white mb-1 group-hover:text-blue-400 transition-colors">
                              {starter.title}
                            </h3>
                            <p className="text-sm text-gray-400 leading-relaxed">
                              {starter.description}
                            </p>
                          </div>
                        </div>
                        <div className="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                          <svg className="w-5 h-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4l8 8-8 8M4 12h16" />
                          </svg>
                        </div>
                      </button>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          ) : (
            /* Chat Messages - Scrollable area with perfect centering */
            <div className={`flex-1 relative ${
              isCanvasOpen && !isCanvasMinimized ? 'overflow-visible' : 'overflow-hidden'
            }`}>
              <div className={`h-full flex ${
                isCanvasOpen && !isCanvasMinimized ? 'justify-start' : 'justify-center'
              }`}>
                <div
                  ref={messagesContainerRef}
                  className={`w-full h-full overflow-y-auto px-6 transition-all duration-300 ${
                    isCanvasOpen && !isCanvasMinimized ? 'max-w-2xl -ml-32' : 'max-w-3xl'
                  }`}
                  onScroll={handleScroll}
                >
                  <div className="pt-0 pb-80">
                    {/* Load More Messages Button */}
                    {currentConversation && messages.length >= 50 && (
                      <div className="text-center py-4">
                        <button
                          onClick={() => loadConversation(currentConversation, true)}
                          disabled={isLoadingHistory}
                          className="px-4 py-2 text-sm text-blue-400 hover:text-blue-300 hover:bg-blue-500/20 rounded-lg transition-colors duration-200 disabled:opacity-50"
                        >
                          {isLoadingHistory ? 'Loading...' : 'Load Earlier Messages'}
                        </button>
                      </div>
                    )}

                    {/* Loading skeleton for conversation messages */}
                    {isLoadingMessages && messages.length === 0 && (
                      <div className="space-y-4">
                        {Array.from({ length: 3 }).map((_, index) => (
                          <div key={index} className="flex justify-start">
                            <div className="w-7 h-7 rounded-full bg-gray-200 animate-pulse mr-3 mt-1 flex-shrink-0"></div>
                            <div className="max-w-[65%] bg-gray-100 rounded-2xl rounded-bl-lg px-4 py-3 animate-pulse">
                              <div className="space-y-2">
                                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                                <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                                <div className="h-4 bg-gray-200 rounded w-5/6"></div>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}

                    {/* Show indicator if messages are truncated */}
                    {messages.length > 100 && (
                      <div className="text-center py-4">
                        <div className="text-sm text-gray-400 bg-white/10 rounded-lg px-4 py-2 inline-block">
                          Showing last 50 of {messages.length} messages for better performance
                        </div>
                      </div>
                    )}

                    {visibleMessages.map((msg, index) => {
                      // Determine spacing based on message sequence
                      const prevMsg = index > 0 ? visibleMessages[index - 1] : null;
                      const isResponseToUser = prevMsg && prevMsg.role === 'user' && msg.role === 'assistant';
                      const spacingClass = index === 0 ? 'pt-3' : isResponseToUser ? 'mt-6' : 'mt-32';

                      return (
                  <div
                    key={msg.id}
                    data-message-id={msg.id}
                    className={`flex ${msg.role === 'user' ? 'justify-end' : 'justify-start'} group ${
                      isCanvasOpen && !isCanvasMinimized ? '-ml-96' : ''
                    } ${
                      msg.role === 'assistant' && isCanvasOpen && !isCanvasMinimized ? 'ml-8' : ''
                    } ${spacingClass}`}
                  >
                <div className={`${
                  msg.role === 'user'
                    ? (isCanvasOpen && !isCanvasMinimized ? 'max-w-[60%]' : 'max-w-[50%]')
                    : (isCanvasOpen && !isCanvasMinimized ? 'max-w-[100%]' : 'max-w-[100%]')
                } relative ${
                  msg.role === 'user'
                    ? 'bg-gray-700/60 text-white rounded-2xl rounded-br-lg shadow-sm border border-gray-600/30'
                    : msg.role === 'assistant'
                    ? 'text-white'
                    : msg.role === 'system'
                    ? 'bg-amber-500/20 text-amber-300 rounded-xl border border-amber-500/30'
                    : 'bg-red-500/20 text-red-300 rounded-xl border border-red-500/30'
                } ${
                  msg.role === 'user' ? 'px-4 py-3' : 'py-3'
                } transition-all duration-300`}>

                  {/* Action buttons for user messages - positioned below the message bubble, show on hover */}
                  {msg.role === 'user' && (
                    <div className="absolute -bottom-8 right-0 z-10 flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                      <CopyButton
                        text={msg.content.filter(part => part.type === 'text').map(part => part.text).join('\n')}
                        variant="message"
                        size="sm"
                        title="Copy message"
                        className="text-gray-400 hover:text-white hover:bg-white/20 rounded-lg cursor-pointer"
                      />
                      <button
                        onClick={() => startEditingMessage(msg.id, msg.content.filter(part => part.type === 'text').map(part => part.text).join('\n'))}
                        className="p-1.5 text-gray-400 hover:text-white hover:bg-white/20 rounded-lg transition-all duration-200 cursor-pointer"
                        title="Edit message"
                      >
                        <PencilSquareIcon className="w-4 h-4 stroke-2" />
                      </button>
                    </div>
                  )}

                  {/* Copy and retry buttons for assistant/system/error messages - positioned below the message box on bottom left, always visible */}
                  {msg.role !== 'user' && (
                    <div className="absolute -bottom-8 left-0 z-10 flex items-center space-x-2">
                      <CopyButton
                        text={msg.content.filter(part => part.type === 'text').map(part => part.text).join('\n')}
                        variant="message"
                        size="sm"
                        title="Copy message"
                      />
                      {msg.role === 'assistant' && selectedConfigId && (
                        <RetryDropdown
                          configId={selectedConfigId}
                          onRetry={(apiKeyId) => handleRetryMessage(index, apiKeyId)}
                          disabled={isLoading}
                        />
                      )}
                    </div>
                  )}

                  <div className="space-y-2 chat-message-content">
                    {/* Edit mode for user messages */}
                    {msg.role === 'user' && editingMessageId === msg.id ? (
                      <div className="space-y-3">
                        <textarea
                          value={editingText}
                          onChange={(e) => setEditingText(e.target.value)}
                          className="w-full p-3 bg-white/20 border border-white/30 rounded-lg text-white placeholder-white/70 focus:outline-none focus:ring-2 focus:ring-white/50 resize-none"
                          placeholder="Edit your message..."
                          rows={3}
                          autoFocus
                        />
                        <div className="flex items-center space-x-2">
                          <button
                            onClick={saveEditedMessage}
                            disabled={!editingText.trim()}
                            className="flex items-center space-x-1 px-3 py-1.5 bg-white/20 hover:bg-white/30 disabled:bg-white/10 disabled:opacity-50 text-white text-sm rounded-lg transition-all duration-200"
                          >
                            <CheckIcon className="w-4 h-4" />
                            <span>Save & Continue</span>
                          </button>
                          <button
                            onClick={cancelEditingMessage}
                            className="flex items-center space-x-1 px-3 py-1.5 bg-white/10 hover:bg-white/20 text-white text-sm rounded-lg transition-all duration-200"
                          >
                            <XCircleIcon className="w-4 h-4" />
                            <span>Cancel</span>
                          </button>
                        </div>
                        <p className="text-white/70 text-xs">
                          💡 Saving will restart the conversation from this point, removing all messages that came after.
                        </p>
                      </div>
                    ) : (
                      /* Normal message display */
                      msg.content.map((part, partIndex) => {
                        if (part.type === 'text') {
                          // Use LazyMarkdownRenderer for assistant messages, plain text for others
                          if (msg.role === 'assistant') {
                            return (
                              <LazyMarkdownRenderer
                                key={partIndex}
                                content={part.text}
                                className="text-[16.5px]"
                              />
                            );
                          } else {
                            return (
                              <div key={partIndex} className="whitespace-pre-wrap break-words leading-relaxed text-[16.5px]">
                                {part.text}
                              </div>
                            );
                          }
                        }
                        if (part.type === 'image_url') {
                          return (
                            <img
                              key={partIndex}
                              src={part.image_url.url}
                              alt="uploaded content"
                              className="max-w-full max-h-48 rounded-xl shadow-sm"
                            />
                          );
                        }
                        return null;
                      })
                    )}
                  </div>
                </div>


                  </div>
                      );
                    })}

                {/* Minimized Canvas Card - appears inline in chat when canvas is minimized */}
                {showOrchestration && orchestrationExecutionId && isCanvasMinimized && (
                  <div className="space-y-6">
                    <MinimizedCanvasCard
                      orchestrationComplete={orchestrationComplete}
                      onMaximize={() => {
                        console.log('🎭 [DEBUG] Maximizing canvas from minimized card');
                        // Trigger the maximize in the OrchestrationCanvas component
                        setTriggerMaximize(true);
                        // Reset the trigger after a brief delay
                        setTimeout(() => setTriggerMaximize(false), 100);
                      }}
                      isCanvasOpen={isCanvasOpen}
                      isCanvasMinimized={isCanvasMinimized}
                    />
                  </div>
                )}

                {isLoading && (
                  <div className="flex justify-start group">

                    <DynamicStatusIndicator
                      currentStage={messageStatus.currentStage}
                      isStreaming={useStreaming && messageStatus.currentStage === 'typing'}
                      orchestrationStatus={orchestrationStatus}
                      onStageChange={(stage) => {
                        console.log(`🎯 UI Status changed to: ${stage}`);
                      }}
                    />

                    {/* Workflow Status Indicator */}
                    {workflowStatus && selectedConfigId.startsWith('workflow_') && (
                      <div className="flex justify-start group mt-2">
                        <div className="bg-gradient-to-r from-purple-900/40 to-blue-900/40 backdrop-blur-sm border border-purple-500/30 rounded-xl px-4 py-2 max-w-md">
                          <div className="flex items-center gap-2">
                            <div className="w-2 h-2 bg-purple-400 rounded-full animate-pulse"></div>
                            <span className="text-sm text-purple-300 font-medium">
                              {workflowStatus}
                            </span>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                )}

                {/* AI Team Orchestration Canvas */}
                {showOrchestration && orchestrationExecutionId && (
                  <Suspense fallback={
                    <div className="flex items-center justify-center p-8">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500"></div>
                      <span className="ml-3 text-gray-600">Loading orchestration canvas...</span>
                    </div>
                  }>
                    <OrchestrationCanvas
                      executionId={orchestrationExecutionId}
                      onCanvasStateChange={handleCanvasStateChange}
                      forceMaximize={triggerMaximize}
                      onComplete={(result: string) => {
                        // Skip auto-completion for test execution IDs
                        if (orchestrationExecutionId?.startsWith('test-execution-id')) {
                          console.log('🎭 [DEBUG] Skipping auto-completion for test execution');
                          setOrchestrationComplete(true); // Still mark as complete for UI
                          return;
                        }

                        console.log('🎉 [ORCHESTRATION] Completed:', result);
                        setOrchestrationComplete(true);

                        // Add the final result as a message
                        const finalMessage: PlaygroundMessage = {
                          id: Date.now().toString() + '-orchestration-final',
                          role: 'assistant',
                          content: [{ type: 'text', text: result }],
                        };
                        setMessages(prevMessages => [...prevMessages, finalMessage]);

                        // Hide orchestration UI
                        setShowOrchestration(false);
                        setOrchestrationExecutionId(null);
                        setOrchestrationComplete(false); // Reset for next orchestration

                        // Save final message
                        if (currentConversation?.id) {
                          saveMessageToDatabase(currentConversation.id, finalMessage).catch(err => {
                            console.error('❌ Failed to save orchestration final message:', err);
                          });
                        }
                      }}
                      onError={(error: string) => {
                        // Skip auto-close for test execution IDs
                        if (orchestrationExecutionId?.startsWith('test-execution-id')) {
                          console.log('🎭 [DEBUG] Ignoring test execution error:', error);
                          return;
                        }

                        console.error('❌ [ORCHESTRATION] Error:', error);
                        setError(`Orchestration error: ${error}`);
                        setShowOrchestration(false);
                        setOrchestrationExecutionId(null);
                      }}
                    />
                  </Suspense>
                )}

                  {/* Invisible element to scroll to */}
                  <div ref={messagesEndRef} />
                  </div>
                </div>
              </div>

              {/* Scroll to bottom button - centered in chat area */}
              {showScrollToBottom && (
                <div className="absolute bottom-6 left-1/2 transform -translate-x-1/2 z-10">
                  <button
                    onClick={() => scrollToBottom(true)}
                    className="w-12 h-12 bg-gray-800/50 rounded-full shadow-lg border border-gray-700/50 flex items-center justify-center hover:shadow-xl transition-all duration-200 hover:scale-105 group"
                    aria-label="Scroll to bottom"
                  >
                    <svg className="w-5 h-5 text-gray-400 group-hover:text-blue-400 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                    </svg>
                  </button>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Fixed Input Area - Script.io inspired design */}
        <div className="fixed bottom-0 z-50 transition-all duration-300 ease-in-out bg-gradient-to-t from-[#040716] via-[#040716]/95 to-transparent" style={{
          left: sidebarWidth,
          right: isCanvasOpen && !isCanvasMinimized
            ? '50%' // Canvas takes 50% of screen width
            : isHistoryCollapsed ? '0px' : '320px'
        }}>
          <div className="px-6 pt-24 pb-2 flex justify-center">
            <div className={`w-full transition-all duration-300 ${
              isCanvasOpen && !isCanvasMinimized ? 'max-w-2xl' : 'max-w-3xl'
            }`}>
              {/* Error Message */}
              {error && (
                <div className="mb-4 bg-red-500/20 border border-red-500/30 rounded-2xl p-4">
                  <div className="flex items-center space-x-2">
                    <svg className="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <p className="text-red-800 text-sm font-medium">{error}</p>
                  </div>
                </div>
              )}



              <form onSubmit={handleSendMessage}>
                {/* Premium Image Previews */}
                {imagePreviews.length > 0 && (
                  <div className="mb-6">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-2">
                        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                        <span className="text-sm font-medium text-white">
                          {imagePreviews.length} image{imagePreviews.length > 1 ? 's' : ''} attached
                        </span>
                      </div>
                      <button
                        type="button"
                        onClick={() => handleRemoveImage()}
                        className="text-xs text-gray-400 hover:text-red-400 transition-colors duration-200 font-medium"
                      >
                        Clear all
                      </button>
                    </div>
                    <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-3">
                      {imagePreviews.map((preview, index) => (
                        <div key={index} className="relative group">
                          <div className="relative overflow-hidden rounded-xl border-2 border-gray-700/50 bg-gray-800/30 shadow-sm hover:shadow-md transition-all duration-200 aspect-square">
                            <img
                              src={preview}
                              alt={`Preview ${index + 1}`}
                              className="w-full h-full object-cover"
                            />
                            <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-all duration-200"></div>
                            <button
                              type="button"
                              onClick={() => handleRemoveImage(index)}
                              className="absolute -top-1 -right-1 w-6 h-6 bg-red-500 hover:bg-red-600 text-white rounded-full flex items-center justify-center transition-all duration-200 shadow-lg opacity-0 group-hover:opacity-100 transform scale-90 group-hover:scale-100"
                              aria-label={`Remove image ${index + 1}`}
                            >
                              <XCircleIcon className="w-3.5 h-3.5" />
                            </button>
                          </div>
                          <div className="absolute bottom-1 right-1 bg-black/70 text-white text-xs px-1.5 py-0.5 rounded-md font-medium">
                            {index + 1}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Input Container - Gemini style */}
                <div className="relative bg-gray-800/30 rounded-2xl border border-gray-700/50 shadow-lg hover:shadow-xl transition-all duration-200 focus-within:ring-2 focus-within:ring-blue-500/20 focus-within:border-gray-600">
                  <div className="flex items-end p-4 space-x-3">
                    {/* Hidden file input */}
                    <input
                      type="file"
                      accept="image/*"
                      multiple
                      onChange={handleImageChange}
                      ref={fileInputRef}
                      className="hidden"
                      id="imageUpload"
                    />

                    {/* Attach button */}
                    <button
                      type="button"
                      onClick={() => fileInputRef.current?.click()}
                      disabled={imageFiles.length >= 10}
                      className={`relative p-2 rounded-xl transition-all duration-200 flex-shrink-0 ${
                        imageFiles.length >= 10
                          ? 'text-gray-600 cursor-not-allowed'
                          : 'text-gray-400 hover:text-blue-400 hover:bg-white/10'
                      }`}
                      aria-label={imageFiles.length >= 10 ? "Maximum 10 images reached" : "Attach images"}
                      title={imageFiles.length >= 10 ? "Maximum 10 images reached" : "Attach images (up to 10)"}
                    >
                      <PaperClipIcon className="w-5 h-5" />
                      {imageFiles.length > 0 && (
                        <div className="absolute -top-1 -right-1 w-4 h-4 bg-orange-500 text-white text-xs rounded-full flex items-center justify-center font-bold">
                          {imageFiles.length}
                        </div>
                      )}
                    </button>

                    {/* Message input */}
                    <div className="flex-1">
                      <textarea
                        value={messageInput}
                        onChange={(e) => setMessageInput(e.target.value)}
                        placeholder={selectedConfigId ? "Type a message..." : "Select a router or workflow first"}
                        disabled={!selectedConfigId || isLoading}
                        rows={1}
                        className="w-full px-0 py-2 bg-transparent border-0 text-white placeholder-gray-500 focus:outline-none focus-visible:outline-none focus:border-none focus:ring-0 disabled:opacity-50 resize-none text-base leading-relaxed [&:focus]:outline-none [&:focus-visible]:outline-none [&:focus]:border-none [&:focus]:ring-0 [&:focus]:shadow-none [&:active]:outline-none [&:active]:border-none [&:active]:ring-0 [&:active]:shadow-none"
                        onKeyDown={(e) => {
                          if (e.key === 'Enter' && !e.shiftKey) {
                            e.preventDefault();
                            if ((messageInput.trim() || imageFiles.length > 0) && selectedConfigId && !isLoading) {
                                handleSendMessage();
                            }
                          }
                        }}
                        style={{
                          minHeight: '24px',
                          maxHeight: '120px',
                          outline: 'none !important',
                          border: 'none !important',
                          boxShadow: 'none !important',
                          WebkitAppearance: 'none',
                          MozAppearance: 'none'
                        }}
                        onInput={(e) => {
                          const target = e.target as HTMLTextAreaElement;
                          target.style.height = 'auto';
                          target.style.height = Math.min(target.scrollHeight, 120) + 'px';
                        }}
                      />
                    </div>

                    {/* Send button */}
                    <button
                      type="submit"
                      disabled={!selectedConfigId || isLoading || (!messageInput.trim() && imageFiles.length === 0)}
                      className="p-2.5 bg-blue-500 hover:bg-blue-600 disabled:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-xl transition-all duration-200 flex items-center justify-center shadow-lg hover:shadow-xl flex-shrink-0"
                      aria-label="Send message"
                      title="Send message"
                    >
                      {isLoading ? (
                        <svg className="w-5 h-5 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                        </svg>
                      ) : (
                        <PaperAirplaneIcon className="w-5 h-5" />
                      )}
                    </button>
                  </div>


                </div>
              </form>
            </div>
          </div>
        </div>
      </div>

      {/* History Sidebar - Gemini inspired */}
      <div className={`fixed top-0 right-0 h-full bg-[#030614] border-l border-gray-800/50 shadow-xl transition-all duration-300 ease-in-out z-30 ${
        isHistoryCollapsed ? 'w-0 overflow-hidden' : 'w-80'
      }`} style={{
        transform: isHistoryCollapsed ? 'translateX(100%)' : 'translateX(0)',
        opacity: isHistoryCollapsed ? 0 : 1
      }}>
        <div className="flex flex-col h-full">
          {/* History Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-800/50">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-white/10 rounded-lg flex items-center justify-center">
                <svg className="w-4 h-4 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div>
                <h2 className="font-semibold text-white">History</h2>
                <p className="text-xs text-gray-400">{chatHistory.length} conversations</p>
              </div>
            </div>
            <button
              onClick={() => setIsHistoryCollapsed(!isHistoryCollapsed)}
              className="p-2 text-gray-400 hover:text-white hover:bg-white/10 rounded-lg transition-all duration-200 hover:scale-105"
              aria-label="Toggle history sidebar"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* New Chat Button */}
          <div className="p-4 border-b border-gray-800/50">
            <button
              onClick={startNewChat}
              className="w-full flex items-center justify-center space-x-2 px-4 py-3 bg-blue-500 hover:bg-blue-600 text-white rounded-xl transition-all duration-200 shadow-sm hover:shadow-md"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
              </svg>
              <span className="font-medium">New Chat</span>
            </button>

          </div>

          {/* Enhanced History List with Virtual Scrolling */}
          <div className="flex-1 overflow-y-auto p-4 space-y-2">
            {isLoadingHistory ? (
              <div className="space-y-2 p-4">
                {Array.from({ length: 8 }).map((_, index) => (
                  <div key={index} className="p-3 rounded-xl border border-gray-700/50 animate-pulse">
                    <div className="bg-gray-700 h-4 w-3/4 rounded mb-2"></div>
                    <div className="bg-gray-700 h-3 w-1/2 rounded"></div>
                  </div>
                ))}
              </div>
            ) : chatHistory.length === 0 ? (
              <div className="text-center py-8">
                <div className="w-12 h-12 bg-white/10 rounded-xl flex items-center justify-center mx-auto mb-3">
                  <svg className="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                  </svg>
                </div>
                <p className="text-sm text-gray-400">No conversations yet</p>
                <p className="text-xs text-gray-500 mt-1">Start chatting to see your history</p>
              </div>
            ) : (
              <>
                {chatHistory.map((chat) => (
                  <ChatHistoryItem
                    key={chat.id}
                    chat={chat}
                    currentConversation={currentConversation}
                    onLoadChat={loadChatFromHistory}
                    onDeleteChat={deleteConversation}
                  />
                ))}
              </>
            )}
          </div>

          {/* Stale data indicator */}
          {isChatHistoryStale && (
            <div className="px-4 py-2 bg-orange-500/20 border-t border-orange-500/30">
              <div className="flex items-center text-xs text-orange-600">
                <svg className="w-3 h-3 mr-1 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                Updating...
              </div>
            </div>
          )}

          {/* Chat history error */}
          {chatHistoryError && (
            <div className="px-4 py-2 bg-red-500/20 border-t border-red-500/30">
              <div className="flex items-center justify-between text-xs text-red-600">
                <span>Failed to load history</span>
                <button
                  onClick={() => refetchChatHistory(true)}
                  className="text-red-700 hover:text-red-800 font-medium"
                >
                  Retry
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* History Toggle Button (when collapsed) */}
      <div className={`fixed top-20 right-4 z-40 transition-all duration-300 ease-in-out ${
        isHistoryCollapsed ? 'opacity-100 scale-100 translate-x-0' : 'opacity-0 scale-95 translate-x-4 pointer-events-none'
      }`}>
        <button
          onClick={() => setIsHistoryCollapsed(false)}
          className="p-3 bg-gray-800/50 border border-gray-700/50 rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 text-gray-400 hover:text-blue-400 hover:scale-105"
          aria-label="Show history sidebar"
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </button>
      </div>
    </div>
  );
}