/**
 * React Hook for WebSocket workflow updates
 * Provides real-time updates for Manual Build workflows
 */

import { useEffect, useRef, useState, useCallback } from 'react';
import { WorkflowWebSocketEvent } from '@/lib/websocket/WorkflowWebSocketServer';
import { workflowWebSocketManager } from '@/lib/websocket/WorkflowWebSocketManager';

export interface WorkflowWebSocketState {
  isConnected: boolean;
  isConnecting: boolean;
  error: string | null;
  lastEvent: WorkflowWebSocketEvent | null;
  events: WorkflowWebSocketEvent[];
  connectionCount: number;
}

export interface WorkflowWebSocketActions {
  connect: () => void;
  disconnect: () => void;
  sendEvent: (eventType: string, data: any, executionId?: string) => Promise<void>;
  clearEvents: () => void;
  reconnect: () => void;
}

export function useWorkflowWebSocket(
  workflowId: string | null,
  options: {
    autoConnect?: boolean;
    maxEvents?: number;
    reconnectInterval?: number;
    onEvent?: (event: WorkflowWebSocketEvent) => void;
    onConnect?: () => void;
    onDisconnect?: () => void;
    onError?: (error: string) => void;
  } = {}
): [WorkflowWebSocketState, WorkflowWebSocketActions] {
  const {
    autoConnect = true,
    maxEvents = 100,
    reconnectInterval = 5000,
    onEvent,
    onConnect,
    onDisconnect,
    onError
  } = options;

  const [state, setState] = useState<WorkflowWebSocketState>({
    isConnected: false,
    isConnecting: false,
    error: null,
    lastEvent: null,
    events: [],
    connectionCount: 0
  });

  const unsubscribeRef = useRef<(() => void) | null>(null);
  const subscriberIdRef = useRef<string>(crypto.randomUUID());
  const isManualDisconnectRef = useRef(false);

  // Connect to WebSocket using the manager
  const connect = useCallback(() => {
    if (!workflowId || state.isConnected || state.isConnecting) {
      return;
    }

    setState(prev => ({ ...prev, isConnecting: true, error: null }));
    isManualDisconnectRef.current = false;

    try {
      // Subscribe to the workflow using the manager
      const unsubscribe = workflowWebSocketManager.subscribe(workflowId, {
        id: subscriberIdRef.current,

        onEvent: (event: WorkflowWebSocketEvent) => {
          console.log(`[Workflow WebSocket] Event received:`, event);

          setState(prev => {
            const newEvents = [...prev.events, event];
            // Keep only the last maxEvents
            if (newEvents.length > maxEvents) {
              newEvents.splice(0, newEvents.length - maxEvents);
            }

            return {
              ...prev,
              lastEvent: event,
              events: newEvents
            };
          });

          onEvent?.(event);
        },
        onConnect: () => {
          console.log(`[Workflow WebSocket] Connected to workflow ${workflowId}`);
          setState(prev => ({
            ...prev,
            isConnected: true,
            isConnecting: false,
            error: null,
            connectionCount: prev.connectionCount + 1
          }));
          onConnect?.();
        },

        onDisconnect: () => {
          console.log(`[Workflow WebSocket] Disconnected from workflow ${workflowId}`);
          setState(prev => ({
            ...prev,
            isConnected: false,
            isConnecting: false
          }));
          onDisconnect?.();
        },
        onError: (error: string) => {
          console.error(`[Workflow WebSocket] Connection error:`, error);
          setState(prev => ({
            ...prev,
            isConnected: false,
            isConnecting: false,
            error
          }));
          onError?.(error);
        }
      });

      unsubscribeRef.current = unsubscribe;

    } catch (error) {
      console.error('[Workflow WebSocket] Failed to subscribe:', error);
      const errorMessage = 'Failed to create connection';
      setState(prev => ({
        ...prev,
        isConnected: false,
        isConnecting: false,
        error: errorMessage
      }));
      onError?.(errorMessage);
    }
  }, [workflowId, maxEvents, onEvent, onConnect, onDisconnect, onError, state.isConnected, state.isConnecting]);

  // Disconnect from WebSocket
  const disconnect = useCallback(() => {
    isManualDisconnectRef.current = true;

    if (unsubscribeRef.current) {
      unsubscribeRef.current();
      unsubscribeRef.current = null;
    }

    setState(prev => ({
      ...prev,
      isConnected: false,
      isConnecting: false,
      error: null
    }));

    onDisconnect?.();
    console.log(`[Workflow WebSocket] Disconnected from workflow ${workflowId}`);
  }, [onDisconnect, workflowId]);

  // Send event to workflow
  const sendEvent = useCallback(async (
    eventType: string,
    data: any,
    executionId?: string
  ): Promise<void> => {
    if (!workflowId) {
      throw new Error('No workflow ID provided');
    }

    try {
      const response = await fetch(`/api/workflow/stream/${workflowId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          eventType,
          data,
          executionId
        })
      });

      if (!response.ok) {
        throw new Error(`Failed to send event: ${response.statusText}`);
      }

      console.log(`[Workflow WebSocket] Sent event ${eventType} to workflow ${workflowId}`);
    } catch (error) {
      console.error('[Workflow WebSocket] Failed to send event:', error);
      throw error;
    }
  }, [workflowId]);

  // Clear events history
  const clearEvents = useCallback(() => {
    setState(prev => ({
      ...prev,
      events: [],
      lastEvent: null
    }));
  }, []);

  // Reconnect
  const reconnect = useCallback(() => {
    disconnect();
    setTimeout(connect, 100);
  }, [disconnect, connect]);

  // Auto-connect on mount or workflowId change
  useEffect(() => {
    if (autoConnect && workflowId && !state.isConnected && !state.isConnecting) {
      connect();
    }
  }, [workflowId, autoConnect, connect, state.isConnected, state.isConnecting]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      // Cleanup without calling disconnect to avoid dependency issues
      isManualDisconnectRef.current = true;

      if (unsubscribeRef.current) {
        unsubscribeRef.current();
        unsubscribeRef.current = null;
      }
    };
  }, []);

  const actions: WorkflowWebSocketActions = {
    connect,
    disconnect,
    sendEvent,
    clearEvents,
    reconnect
  };

  return [state, actions];
}
