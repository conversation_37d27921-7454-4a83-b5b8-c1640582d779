/**
 * Singleton WebSocket Manager for Workflow Connections
 * Ensures only one EventSource connection per workflow exists globally
 */

import { WorkflowWebSocketEvent } from './WorkflowWebSocketServer';

interface WorkflowConnection {
  eventSource: EventSource;
  subscribers: Set<string>;
  workflowId: string;
  createdAt: number;
}

interface Subscriber {
  id: string;
  onEvent: (event: WorkflowWebSocketEvent) => void;
  onConnect?: () => void;
  onDisconnect?: () => void;
  onError?: (error: string) => void;
}

class WorkflowWebSocketManager {
  private connections = new Map<string, WorkflowConnection>();
  private subscribers = new Map<string, Subscriber>();

  /**
   * Subscribe to workflow events
   */
  subscribe(
    workflowId: string,
    subscriber: Subscriber
  ): () => void {
    console.log(`[WebSocket Manager] Subscribing ${subscriber.id} to workflow ${workflowId}`);
    
    // Store subscriber
    this.subscribers.set(subscriber.id, subscriber);

    // Get or create connection
    let connection = this.connections.get(workflowId);
    
    if (!connection) {
      console.log(`[WebSocket Manager] Creating new connection for workflow ${workflowId}`);
      connection = this.createConnection(workflowId);
      this.connections.set(workflowId, connection);
    }

    // Add subscriber to connection
    connection.subscribers.add(subscriber.id);
    console.log(`[WebSocket Manager] Added subscriber ${subscriber.id} to workflow ${workflowId} (${connection.subscribers.size} total)`);

    // If connection is already established, notify subscriber
    if (connection.eventSource.readyState === EventSource.OPEN) {
      subscriber.onConnect?.();
    }

    // Return unsubscribe function
    return () => this.unsubscribe(workflowId, subscriber.id);
  }

  /**
   * Unsubscribe from workflow events
   */
  private unsubscribe(workflowId: string, subscriberId: string): void {
    console.log(`[WebSocket Manager] Unsubscribing ${subscriberId} from workflow ${workflowId}`);
    
    const connection = this.connections.get(workflowId);
    if (!connection) return;

    // Remove subscriber
    connection.subscribers.delete(subscriberId);
    this.subscribers.delete(subscriberId);

    console.log(`[WebSocket Manager] Removed subscriber ${subscriberId} from workflow ${workflowId} (${connection.subscribers.size} remaining)`);

    // If no more subscribers, close connection
    if (connection.subscribers.size === 0) {
      console.log(`[WebSocket Manager] No more subscribers for workflow ${workflowId}, closing connection`);
      connection.eventSource.close();
      this.connections.delete(workflowId);
    }
  }

  /**
   * Create a new EventSource connection
   */
  private createConnection(workflowId: string): WorkflowConnection {
    const eventSource = new EventSource(`/api/workflow/stream/${workflowId}`);
    
    const connection: WorkflowConnection = {
      eventSource,
      subscribers: new Set(),
      workflowId,
      createdAt: Date.now()
    };

    // Set up event handlers
    eventSource.onopen = () => {
      console.log(`[WebSocket Manager] Connection opened for workflow ${workflowId}`);
      this.notifySubscribers(workflowId, 'onConnect');
    };

    eventSource.onmessage = (event) => {
      try {
        const data: WorkflowWebSocketEvent = JSON.parse(event.data);
        console.log(`[WebSocket Manager] Event received for workflow ${workflowId}:`, data.type);
        this.notifySubscribers(workflowId, 'onEvent', data);
      } catch (error) {
        console.error(`[WebSocket Manager] Failed to parse event for workflow ${workflowId}:`, error);
        this.notifySubscribers(workflowId, 'onError', 'Failed to parse event data');
      }
    };

    eventSource.onerror = (error) => {
      console.error(`[WebSocket Manager] Connection error for workflow ${workflowId}:`, error);
      this.notifySubscribers(workflowId, 'onError', 'Connection error');
      
      // Clean up connection on error
      this.connections.delete(workflowId);
    };

    return connection;
  }

  /**
   * Notify all subscribers of a connection
   */
  private notifySubscribers(
    workflowId: string,
    method: 'onEvent' | 'onConnect' | 'onDisconnect' | 'onError',
    data?: any
  ): void {
    const connection = this.connections.get(workflowId);
    if (!connection) return;

    connection.subscribers.forEach(subscriberId => {
      const subscriber = this.subscribers.get(subscriberId);
      if (!subscriber) return;

      try {
        switch (method) {
          case 'onEvent':
            subscriber.onEvent(data);
            break;
          case 'onConnect':
            subscriber.onConnect?.();
            break;
          case 'onDisconnect':
            subscriber.onDisconnect?.();
            break;
          case 'onError':
            subscriber.onError?.(data);
            break;
        }
      } catch (error) {
        console.error(`[WebSocket Manager] Error notifying subscriber ${subscriberId}:`, error);
      }
    });
  }

  /**
   * Get connection status for a workflow
   */
  getConnectionStatus(workflowId: string): {
    isConnected: boolean;
    subscriberCount: number;
  } {
    const connection = this.connections.get(workflowId);
    return {
      isConnected: connection?.eventSource.readyState === EventSource.OPEN || false,
      subscriberCount: connection?.subscribers.size || 0
    };
  }

  /**
   * Force disconnect a workflow connection
   */
  forceDisconnect(workflowId: string): void {
    const connection = this.connections.get(workflowId);
    if (connection) {
      console.log(`[WebSocket Manager] Force disconnecting workflow ${workflowId}`);
      connection.eventSource.close();
      this.connections.delete(workflowId);
      
      // Notify all subscribers
      this.notifySubscribers(workflowId, 'onDisconnect');
      
      // Clear subscribers
      connection.subscribers.forEach(subscriberId => {
        this.subscribers.delete(subscriberId);
      });
    }
  }
}

// Export singleton instance
export const workflowWebSocketManager = new WorkflowWebSocketManager();
